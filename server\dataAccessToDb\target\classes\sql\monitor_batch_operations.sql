-- 批量操作监控脚本
-- 在执行批量操作前后运行这些查询来观察效果

-- 1. 执行前：记录当前状态
SELECT 
    'BEFORE_TEST' as test_phase,
    CURRENT_TIMESTAMP as check_time,
    (SELECT COUNT(*) FROM SHIP.SHIP_STATICINFO) as static_count,
    (SELECT COUNT(*) FROM SHIP.SHIP_DYNAMICTION) as dynamic_count,
    (SELECT COUNT(*) FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ) as track_count;

-- 2. 监控最近1分钟的数据变化
SELECT 
    'RECENT_CHANGES' as monitor_type,
    table_name,
    record_count,
    last_update
FROM (
    SELECT 
        'SHIP_STATICINFO' as table_name,
        COUNT(*) as record_count,
        MAX(LOADTIME) as last_update
    FROM SHIP.SHIP_STATICINFO 
    WHERE LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' MINUTE
    
    UNION ALL
    
    SELECT 
        'SHIP_DYNAMICTION' as table_name,
        COUNT(*) as record_count,
        MAX(LOADTIME) as last_update
    FROM SHIP.SHIP_DYNAMICTION 
    WHERE LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' MINUTE
    
    UNION ALL
    
    SELECT 
        'SHIP_HISTORYTRACKPOINT_HQ' as table_name,
        COUNT(*) as record_count,
        MAX(LOADTIME) as last_update
    FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ 
    WHERE LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' MINUTE
) t
ORDER BY table_name;

-- 3. 查看测试数据的插入情况
SELECT 
    'TEST_DATA_ANALYSIS' as analysis_type,
    data_type,
    COUNT(*) as count,
    MIN(LOADTIME) as first_insert,
    MAX(LOADTIME) as last_insert
FROM (
    -- 静态信息中的测试数据
    SELECT 
        'STATIC_AIS_TEST' as data_type,
        LOADTIME
    FROM SHIP.SHIP_STATICINFO 
    WHERE (SHIPNAME LIKE '%测试%' OR SHIPNAME LIKE '%AIS测试%' OR MMSI BETWEEN 100000000 AND 199999999)
    AND LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' HOUR
    
    UNION ALL
    
    SELECT 
        'STATIC_BD_TEST' as data_type,
        LOADTIME
    FROM SHIP.SHIP_STATICINFO 
    WHERE (SHIPNAME LIKE '%北斗测试%' OR BDID LIKE 'BD%')
    AND LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' HOUR
    
    UNION ALL
    
    -- 动态信息中的测试数据
    SELECT 
        'DYNAMIC_AIS_TEST' as data_type,
        LOADTIME
    FROM SHIP.SHIP_DYNAMICTION 
    WHERE (SHIPNAME LIKE '%测试%' OR SHIPNAME LIKE '%AIS测试%' OR MMSI BETWEEN 100000000 AND 199999999)
    AND LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' HOUR
    
    UNION ALL
    
    SELECT 
        'DYNAMIC_BD_TEST' as data_type,
        LOADTIME
    FROM SHIP.SHIP_DYNAMICTION 
    WHERE (SHIPNAME LIKE '%北斗测试%' OR BDID LIKE 'BD%')
    AND LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' HOUR
    
    UNION ALL
    
    -- 轨迹信息中的测试数据
    SELECT 
        'TRACK_TEST' as data_type,
        LOADTIME
    FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ 
    WHERE (BDID LIKE 'BD%' OR MMSI BETWEEN 100000000 AND 199999999)
    AND LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' HOUR
) test_data
GROUP BY data_type
ORDER BY data_type;

-- 4. 分析批量操作的时间分布
SELECT 
    'BATCH_TIMING_ANALYSIS' as analysis_type,
    time_bucket,
    operation_type,
    COUNT(*) as operations_count
FROM (
    SELECT 
        DATE_TRUNC('second', LOADTIME) as time_bucket,
        'STATIC_INSERT' as operation_type
    FROM SHIP.SHIP_STATICINFO 
    WHERE LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '10' MINUTE
    AND (SHIPNAME LIKE '%测试%' OR BDID LIKE 'BD%' OR MMSI BETWEEN 100000000 AND 199999999)
    
    UNION ALL
    
    SELECT 
        DATE_TRUNC('second', LOADTIME) as time_bucket,
        'DYNAMIC_INSERT' as operation_type
    FROM SHIP.SHIP_DYNAMICTION 
    WHERE LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '10' MINUTE
    AND (SHIPNAME LIKE '%测试%' OR BDID LIKE 'BD%' OR MMSI BETWEEN 100000000 AND 199999999)
    
    UNION ALL
    
    SELECT 
        DATE_TRUNC('second', LOADTIME) as time_bucket,
        'TRACK_INSERT' as operation_type
    FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ 
    WHERE LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '10' MINUTE
    AND (BDID LIKE 'BD%' OR MMSI BETWEEN 100000000 AND 199999999)
) timing_data
GROUP BY time_bucket, operation_type
HAVING COUNT(*) > 1  -- 只显示批量操作（一秒内多条记录）
ORDER BY time_bucket DESC, operation_type;

-- 5. 检查数据完整性
SELECT 
    'DATA_INTEGRITY_CHECK' as check_type,
    integrity_issue,
    COUNT(*) as issue_count
FROM (
    -- 检查孤立的动态信息
    SELECT 
        'ORPHANED_DYNAMIC' as integrity_issue
    FROM SHIP.SHIP_DYNAMICTION d
    LEFT JOIN SHIP.SHIP_STATICINFO s ON d.STATICSHIPID = s.ID
    WHERE s.ID IS NULL
    AND d.LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' HOUR
    AND (d.SHIPNAME LIKE '%测试%' OR d.BDID LIKE 'BD%' OR d.MMSI BETWEEN 100000000 AND 199999999)
    
    UNION ALL
    
    -- 检查孤立的轨迹信息
    SELECT 
        'ORPHANED_TRACK' as integrity_issue
    FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ h
    LEFT JOIN SHIP.SHIP_STATICINFO s ON h.STATICSHIPID = s.ID
    WHERE s.ID IS NULL
    AND h.LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' HOUR
    AND (h.BDID LIKE 'BD%' OR h.MMSI BETWEEN 100000000 AND 199999999)
) integrity_data
GROUP BY integrity_issue;

-- 6. 性能指标查询（如果有权限访问系统表）
/*
SELECT 
    'PERFORMANCE_METRICS' as metric_type,
    'ACTIVE_SESSIONS' as metric_name,
    COUNT(*) as metric_value
FROM V$SESSION 
WHERE STATUS = 'ACTIVE'
AND USERNAME = 'SHIP';
*/

-- 7. 清理测试数据的准备语句（执行前请确认）
/*
-- 注意：这些是清理语句，请谨慎使用
-- 清理静态信息测试数据
DELETE FROM SHIP.SHIP_STATICINFO 
WHERE (SHIPNAME LIKE '%测试%' OR BDID LIKE 'BD%' OR MMSI BETWEEN 100000000 AND 199999999)
AND LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' DAY;

-- 清理动态信息测试数据
DELETE FROM SHIP.SHIP_DYNAMICTION 
WHERE (SHIPNAME LIKE '%测试%' OR BDID LIKE 'BD%' OR MMSI BETWEEN 100000000 AND 199999999)
AND LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' DAY;

-- 清理轨迹测试数据
DELETE FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ 
WHERE (BDID LIKE 'BD%' OR MMSI BETWEEN 100000000 AND 199999999)
AND LOADTIME >= CURRENT_TIMESTAMP - INTERVAL '1' DAY;

COMMIT;
*/
