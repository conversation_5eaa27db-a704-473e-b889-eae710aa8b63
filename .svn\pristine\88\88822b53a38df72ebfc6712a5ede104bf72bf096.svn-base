package com.bdstar.data.service;

import com.bdstar.data.mapper.IPositionInfoMapper;
import com.bdstar.data.model.*;
import com.bdstar.data.util.LoggerUtil;
import com.bdstar.data.util.StringUtil;
import com.bdstar.data.util.Utils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import static java.lang.Math.*;

@Service
public class PositionInfoService {

    @Resource
    private IPositionInfoMapper positionInfoMapper;
    @Resource
    private LoggerUtil loggerUtil;
    @Resource
    private StringUtil stringUtil;

    List<ShipDynamicInfo> shipList = new ArrayList<>();

    public void insertPositionInfo(@Param("positionInfoModel") PositionInfoModel positionInfoModel){
        try {
            if (!stringUtil.isEmpty(positionInfoModel)) {
                positionInfoMapper.insertPositionInfo(positionInfoModel,stringUtil.getDateByTimeStamp(Long.parseLong(positionInfoModel.getBasePositionModel().getPosTime()) * 1000));
            }
        }catch (Exception e){
            loggerUtil.getInfoLogger().info("执行出现错误，请查阅错误日志");
            loggerUtil.getErrLogger().info("数据库插入失败:异常类[{}]:异常信息[{}]",e.getClass().getName(),e.getMessage());
            e.printStackTrace();
        }
    }

    public void updateLastPosition(@Param("positionInfoModel") PositionInfoModel positionInfoModel){
        long startTime = System.currentTimeMillis();
        try {
            if (!stringUtil.isEmpty(positionInfoModel)) {
                positionInfoMapper.updateLastPosition(positionInfoModel,stringUtil.getDateByTimeStamp(Long.parseLong(positionInfoModel.getBasePositionModel().getPosTime()) * 1000));
            }
        }catch (Exception e){
            loggerUtil.getInfoLogger().info("执行出现错误，请查阅错误日志");
            loggerUtil.getErrLogger().info("数据库更新失败:异常类[{}]:异常信息[{}]",e.getClass().getName(),e.getMessage());
            e.printStackTrace();
        }
    }

    public void updateShipInfo(@Param("positionInfoModel") PositionInfoModel positionInfoModel){

        int shipId = positionInfoModel.getBasePositionModel().getShipId();
        String bdId = positionInfoModel.getBasePositionModel().getTermId().trim();
        int lon = positionInfoModel.getBasePositionModel().getLongitude() * 10;
        int lat = positionInfoModel.getBasePositionModel().getLatitude() * 10;
        float speed = positionInfoModel.getBasePositionModel().getSpeed();
        speed = (float) (speed * 0.1 * 3600/1852);
        speed = Float.parseFloat(String.format("%.2f", speed));
        int cog = positionInfoModel.getBasePositionModel().getAzimuth()*2;
        int posTime = Integer.parseInt(positionInfoModel.getBasePositionModel().getPosTime());
        loggerUtil.getInfoLogger().info("bdId:"+ bdId +",BDDATA:postime:"+posTime+",trans:"+Utils.GetStringTimeFromLong(posTime)+",now:"+Utils.GetNowTimeString());

        int staticCount = positionInfoMapper.selectStaticInfo(bdId);

        if(staticCount > 0){
            ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
            shipDynamicInfo.setSHIPNAME(bdId);
            shipDynamicInfo.setBDID(bdId);
            shipDynamicInfo.setLON(lon);
            shipDynamicInfo.setLAT(lat);
            shipDynamicInfo.setSPEED(speed);
            shipDynamicInfo.setCOG(cog);
            shipDynamicInfo.setReportTime(posTime);
            shipDynamicInfo.setLastPosTermNo(bdId);
            shipDynamicInfo.setBdTime(Utils.GetStringTimeFromLong(posTime));

            positionInfoMapper.updateDynamicShipInfo_byBDId(shipDynamicInfo);
            loggerUtil.getInfoLogger().info("BD动态信息更新成功");

            int staticId = positionInfoMapper.selectStaticIdByBdId(bdId);
            shipDynamicInfo.setStaticShipId(staticId);
            positionInfoMapper.insertHistoryTrack(shipDynamicInfo);

        }
/*
        else
        {
            ShipStaticInfo shipStaticInfo = new ShipStaticInfo();
            ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
            shipStaticInfo.setSHIPID(shipId);
            String tmBdId = "";
            ShipStaticInfo ship = positionInfoMapper.getFisherByShipId(shipId);
            if (ship == null) return;
            shipStaticInfo.setBOutside(0);//0=本市
            shipStaticInfo.setSHIPNAME(ship.getSHIPNAME());
            shipStaticInfo.setOwner(ship.getOwner());
            shipStaticInfo.setSHIPTYPE(2);

            List<Bdid> bdids = positionInfoMapper.selectTermBdIdByShipId(shipId);
            for (Bdid bdid: bdids){
                if(!tmBdId.contains(bdid.getBdid())){
                    tmBdId += bdid.getBdid() + ",";
                }
            }

            try {
                List<String> mmsi = positionInfoMapper.selectMmsiByShipId(shipId);
                if (mmsi.size() > 0){
                    shipStaticInfo.setMMSI(Integer.parseInt(mmsi.get(0)));
                    shipDynamicInfo.setMMSI(Integer.parseInt(mmsi.get(0)));
                }

            }catch (Exception e){

            }

            if(tmBdId.substring(tmBdId.length()-1).equals(","))
                tmBdId = tmBdId.substring(0,tmBdId.length() - 1);
            shipStaticInfo.setBDID(tmBdId);
            shipDynamicInfo.setBDID(tmBdId);

            shipDynamicInfo.setSHIPNAME(shipStaticInfo.getSHIPNAME());
            shipDynamicInfo.setSHIPTYPE(shipStaticInfo.getSHIPTYPE());
            shipDynamicInfo.setLON(lon);
            shipDynamicInfo.setLAT(lat);
            shipDynamicInfo.setSPEED(speed);
            shipDynamicInfo.setCOG(cog);
            shipDynamicInfo.setReportTime(Integer.parseInt(posTime));

            positionInfoMapper.insertStaticShipInfo(shipStaticInfo);
            //loggerUtil.getInfoLogger().info("静态信息插入---完成");
            int staticId = positionInfoMapper.selectStaticIdByBdId(bdId);
            shipDynamicInfo.setStaticShipId(staticId);
            positionInfoMapper.insertDynamicShipInfo(shipDynamicInfo);
            //loggerUtil.getInfoLogger().info("动态信息插入---完成");
            positionInfoMapper.insertHistoryTrack(shipDynamicInfo);
        }
*/


    }

    public void updateShipInfo_AIS(@Param("aisPosInfoModel")AisPosInfoModel aisPosInfoModel){

        int mmsi = aisPosInfoModel.getTermNo();
        int staticCount = positionInfoMapper.selectStaticInfoByMMSI(mmsi);
        int lon = aisPosInfoModel.getLongitude() * 10;
        int lat = aisPosInfoModel.getLatitude() * 10;

        float speed = aisPosInfoModel.getSpeed()/100;
        int cog = aisPosInfoModel.getAzimuth()/100;
        long posTime = aisPosInfoModel.getPosTime();
        String shipName = positionInfoMapper.getAISShipNameByMMSI(mmsi);
        if(staticCount > 0){
            ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
            shipDynamicInfo.setMMSI(mmsi);
            shipDynamicInfo.setSHIPNAME(shipName);
            shipDynamicInfo.setLON(lon);
            shipDynamicInfo.setLAT(lat);
            shipDynamicInfo.setSPEED(speed);
            shipDynamicInfo.setCOG(cog);
            shipDynamicInfo.setReportTime((int)posTime);
            shipDynamicInfo.setLastPosTermNo(mmsi+"");
            shipDynamicInfo.setAisTime(Utils.GetStringTimeFromLong(posTime));
            positionInfoMapper.updateDynamicShipInfo_byMMSI(shipDynamicInfo);
            loggerUtil.getInfoLogger().info("AIS动态信息更新成功");

            int staticId = positionInfoMapper.selectStaticIdByMMSI(mmsi);
            shipDynamicInfo.setStaticShipId(staticId);
            positionInfoMapper.insertHistoryTrack(shipDynamicInfo);
            loggerUtil.getInfoLogger().info("AIS轨迹插入-----结束" + mmsi);
        }
        /*else{
            ShipStaticInfo shipStaticInfo = new ShipStaticInfo();
            shipStaticInfo.setMMSI(mmsi);
            shipStaticInfo.setSHIPNAME(shipName);

            ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
            shipDynamicInfo.setSHIPNAME(shipName);
            shipDynamicInfo.setMMSI(mmsi);
            shipDynamicInfo.setLON(lon);
            shipDynamicInfo.setLAT(lat);
            shipDynamicInfo.setSPEED(speed);
            shipDynamicInfo.setCOG(cog);
            shipDynamicInfo.setReportTime((int)posTime);

            positionInfoMapper.insertStaticShipInfo(shipStaticInfo);
            loggerUtil.getInfoLogger().info("AIS静态信息插入---完成");
            int staticId = positionInfoMapper.selectStaticIdByMMSI(mmsi);
            shipDynamicInfo.setStaticShipId(staticId);
            positionInfoMapper.insertDynamicShipInfo(shipDynamicInfo);
            loggerUtil.getInfoLogger().info("AIS动态信息插入---完成");
        }*/

        /*loggerUtil.getInfoLogger().info("AIS轨迹插入-----开始" + mmsi);
        //插入轨迹表
        ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
        shipDynamicInfo.setMMSI(mmsi);
        shipDynamicInfo.setLON(lon);
        shipDynamicInfo.setLAT(lat);
        shipDynamicInfo.setSPEED(speed);
        shipDynamicInfo.setCOG(cog);
        shipDynamicInfo.setReportTime((int)posTime);
        int staticId = positionInfoMapper.selectStaticIdByMMSI(mmsi);
        shipDynamicInfo.setStaticShipId(staticId);
        positionInfoMapper.insertHistoryTrack(shipDynamicInfo);
        loggerUtil.getInfoLogger().info("AIS轨迹插入-----结束" + mmsi);*/
    }

    public void updateShipInfo_HQ(@Param("hqPositionModel")HqPositionModel hqPositionModel){
        // 单条数据处理，调用批量处理方法
        List<HqPositionModel> singleList = new ArrayList<>();
        singleList.add(hqPositionModel);
        batchUpdateShipInfo_HQ(singleList);
    }

    /**
     * 批量处理HQ船舶信息更新 - 真正的批量操作优化
     * @param hqPositionModelList HQ位置信息列表
     */
    public void batchUpdateShipInfo_HQ(@Param("hqPositionModelList") List<HqPositionModel> hqPositionModelList){
        if(hqPositionModelList == null || hqPositionModelList.isEmpty()) return;

        // 过滤出有效的数据（终端类型为1）
        List<HqPositionModel> validModels = hqPositionModelList.stream()
                .filter(model -> model.getTermType() == 1)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        if(validModels.isEmpty()) return;

        // 分离AIS和非AIS数据
        List<HqPositionModel> aisModels = new ArrayList<>();
        List<HqPositionModel> bdModels = new ArrayList<>();

        for(HqPositionModel model : validModels){
            String bdId = model.getTermNo().trim();
            if(bdId.length() == 9){
                aisModels.add(model);
            } else {
                bdModels.add(model);
            }
        }

        // 批量处理非AIS数据（北斗数据）
        if(!bdModels.isEmpty()){
            processBatchBdData(bdModels);
        }

        // 批量处理AIS数据
        if(!aisModels.isEmpty()){
            processBatchAisData(aisModels);
        }

        loggerUtil.getInfoLogger().info("HQ批量数据处理完成，总计处理：{} 条，北斗：{} 条，AIS：{} 条",
                validModels.size(), bdModels.size(), aisModels.size());
    }

    /**
     * 批量处理北斗数据
     */
    private void processBatchBdData(List<HqPositionModel> bdModels){
        // 收集需要查询的BDID
        List<String> bdIdList = bdModels.stream()
                .map(model -> model.getTermNo().trim())
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        // 批量查询现有静态信息
        Map<String, Integer> existingStaticMap = new HashMap<>();
        for(String bdId : bdIdList){
            int count = positionInfoMapper.selectStaticInfo(bdId);
            if(count > 0){
                existingStaticMap.put(bdId, positionInfoMapper.selectStaticIdByBdId(bdId));
            }
        }

        // 分离需要插入和更新的数据
        List<ShipStaticInfo> staticInsertList = new ArrayList<>();
        List<ShipDynamicInfo> dynamicInsertList = new ArrayList<>();
        List<ShipDynamicInfo> dynamicUpdateList = new ArrayList<>();
        List<ShipDynamicInfo> trackList = new ArrayList<>();

        for(HqPositionModel model : bdModels){
            String bdId = model.getTermNo().trim();

            // 统一处理位置和时间数据
            int lon = model.getLongitude() * 10;
            int lat = model.getLatitude() * 10;
            float speed = model.getSpeed();
            speed = (float) (speed * 0.1 * 3600/1852);
            speed = Float.parseFloat(String.format("%.2f", speed));
            int cog = model.getAzimuth()*2;
            long posTime = model.getPosTime();

            // 创建动态信息对象
            ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
            shipDynamicInfo.setBDID(bdId);
            shipDynamicInfo.setLON(lon);
            shipDynamicInfo.setLAT(lat);
            shipDynamicInfo.setSPEED(speed);
            shipDynamicInfo.setCOG(cog);
            shipDynamicInfo.setReportTime((int)posTime);

            if(existingStaticMap.containsKey(bdId)){
                // 静态信息已存在，准备更新动态信息
                shipDynamicInfo.setLastPosTermNo(bdId);
                shipDynamicInfo.setBdTime(Utils.GetStringTimeFromLong(posTime));
                dynamicUpdateList.add(shipDynamicInfo);

                // 准备轨迹数据
                ShipDynamicInfo trackInfo = new ShipDynamicInfo();
                trackInfo.setBDID(bdId);
                trackInfo.setLON(lon);
                trackInfo.setLAT(lat);
                trackInfo.setSPEED(speed);
                trackInfo.setCOG(cog);
                trackInfo.setReportTime((int)posTime);
                trackInfo.setStaticShipId(existingStaticMap.get(bdId));
                trackList.add(trackInfo);
            } else {
                // 静态信息不存在，准备插入
                ShipStaticInfo shipStaticInfo = new ShipStaticInfo();
                shipStaticInfo.setSHIPNAME(model.getShipName());
                shipStaticInfo.setBDID(bdId);
                shipStaticInfo.setBOutside(1);
                shipStaticInfo.setOwner(model.getShipOwner());
                shipStaticInfo.setLXDH(model.getShipTel());
                shipStaticInfo.setSHIPTYPE(2);
                staticInsertList.add(shipStaticInfo);

                // 准备动态信息插入（需要在静态信息插入后获取ID）
                shipDynamicInfo.setSHIPNAME(model.getShipName());
                shipDynamicInfo.setSHIPTYPE(2);
                shipDynamicInfo.setAisTime(Utils.GetStringTimeFromLong(posTime));
                dynamicInsertList.add(shipDynamicInfo);
            }
        }

        // 执行批量操作
        if(!staticInsertList.isEmpty()){
            positionInfoMapper.batchInsertStaticShipInfo(staticInsertList);
            loggerUtil.getInfoLogger().info("HQ批量插入静态信息完成：{} 条", staticInsertList.size());

            // 获取新插入的静态ID并设置到动态信息中
            for(int i = 0; i < staticInsertList.size(); i++){
                String bdId = staticInsertList.get(i).getBDID();
                int staticId = positionInfoMapper.selectStaticIdByBdId(bdId);
                dynamicInsertList.get(i).setStaticShipId(staticId);

                // 准备轨迹数据
                ShipDynamicInfo trackInfo = new ShipDynamicInfo();
                trackInfo.setBDID(bdId);
                trackInfo.setLON(dynamicInsertList.get(i).getLON());
                trackInfo.setLAT(dynamicInsertList.get(i).getLAT());
                trackInfo.setSPEED(dynamicInsertList.get(i).getSPEED());
                trackInfo.setCOG(dynamicInsertList.get(i).getCOG());
                trackInfo.setReportTime(dynamicInsertList.get(i).getReportTime());
                trackInfo.setStaticShipId(staticId);
                trackList.add(trackInfo);
            }
        }

        if(!dynamicInsertList.isEmpty()){
            positionInfoMapper.batchInsertDynamicShipInfo(dynamicInsertList);
            loggerUtil.getInfoLogger().info("HQ批量插入动态信息完成：{} 条", dynamicInsertList.size());
        }

        if(!dynamicUpdateList.isEmpty()){
            positionInfoMapper.batchUpdateDynamicShipInfo_byBDId(dynamicUpdateList);
            loggerUtil.getInfoLogger().info("HQ批量更新动态信息完成：{} 条", dynamicUpdateList.size());
        }

        if(!trackList.isEmpty()){
            positionInfoMapper.batchInsertHistoryTrack_HQ(trackList);
            loggerUtil.getInfoLogger().info("HQ批量插入轨迹信息完成：{} 条", trackList.size());
        }
    }

    /**
     * 批量处理AIS数据
     */
    private void processBatchAisData(List<HqPositionModel> aisModels){
        // 收集需要查询的MMSI
        List<Integer> mmsiList = aisModels.stream()
                .map(model -> Integer.parseInt(model.getTermNo().trim()))
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        // 批量查询现有静态信息
        Map<Integer, Integer> existingStaticMap = new HashMap<>();
        for(Integer mmsi : mmsiList){
            int count = positionInfoMapper.selectStaticInfoByMMSI(mmsi);
            if(count > 0){
                existingStaticMap.put(mmsi, positionInfoMapper.selectStaticIdByMMSI(mmsi));
            }
        }

        // 分离需要插入和更新的数据
        List<ShipStaticInfo> staticInsertList = new ArrayList<>();
        List<ShipDynamicInfo> dynamicInsertList = new ArrayList<>();
        List<ShipDynamicInfo> dynamicUpdateList = new ArrayList<>();
        List<ShipDynamicInfo> trackList = new ArrayList<>();

        for(HqPositionModel model : aisModels){
            int mmsi = Integer.parseInt(model.getTermNo().trim());

            // 统一处理位置和时间数据
            int lon = model.getLongitude() * 10;
            int lat = model.getLatitude() * 10;
            float speed = model.getSpeed();
            speed = (float) (speed * 0.1 * 3600/1852);
            speed = Float.parseFloat(String.format("%.2f", speed));
            int cog = model.getAzimuth()*2;
            long posTime = model.getPosTime();

            // 创建动态信息对象
            ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
            shipDynamicInfo.setMMSI(mmsi);
            shipDynamicInfo.setLON(lon);
            shipDynamicInfo.setLAT(lat);
            shipDynamicInfo.setSPEED(speed);
            shipDynamicInfo.setCOG(cog);
            shipDynamicInfo.setReportTime((int)posTime);

            if(existingStaticMap.containsKey(mmsi)){
                // 静态信息已存在，准备更新动态信息
                shipDynamicInfo.setLastPosTermNo(mmsi + "");
                shipDynamicInfo.setAisTime(Utils.GetStringTimeFromLong(posTime));
                dynamicUpdateList.add(shipDynamicInfo);

                // 准备轨迹数据
                ShipDynamicInfo trackInfo = new ShipDynamicInfo();
                trackInfo.setMMSI(mmsi);
                trackInfo.setLON(lon);
                trackInfo.setLAT(lat);
                trackInfo.setSPEED(speed);
                trackInfo.setCOG(cog);
                trackInfo.setReportTime((int)posTime);
                trackInfo.setStaticShipId(existingStaticMap.get(mmsi));
                trackList.add(trackInfo);
            } else {
                // 静态信息不存在，准备插入
                ShipStaticInfo shipStaticInfo = new ShipStaticInfo();
                shipStaticInfo.setSHIPNAME(model.getShipName());
                shipStaticInfo.setMMSI(mmsi);
                shipStaticInfo.setBOutside(1);
                shipStaticInfo.setOwner(model.getShipOwner());
                shipStaticInfo.setLXDH(model.getShipTel());
                staticInsertList.add(shipStaticInfo);

                // 准备动态信息插入（需要在静态信息插入后获取ID）
                shipDynamicInfo.setSHIPNAME(model.getShipName());
                dynamicInsertList.add(shipDynamicInfo);
            }
        }

        // 执行批量操作
        if(!staticInsertList.isEmpty()){
            positionInfoMapper.batchInsertStaticShipInfo(staticInsertList);
            loggerUtil.getInfoLogger().info("HQ批量插入AIS静态信息完成：{} 条", staticInsertList.size());

            // 获取新插入的静态ID并设置到动态信息中
            for(int i = 0; i < staticInsertList.size(); i++){
                int mmsi = staticInsertList.get(i).getMMSI();
                int staticId = positionInfoMapper.selectStaticIdByMMSI(mmsi);
                dynamicInsertList.get(i).setStaticShipId(staticId);

                // 准备轨迹数据
                ShipDynamicInfo trackInfo = new ShipDynamicInfo();
                trackInfo.setMMSI(mmsi);
                trackInfo.setLON(dynamicInsertList.get(i).getLON());
                trackInfo.setLAT(dynamicInsertList.get(i).getLAT());
                trackInfo.setSPEED(dynamicInsertList.get(i).getSPEED());
                trackInfo.setCOG(dynamicInsertList.get(i).getCOG());
                trackInfo.setReportTime(dynamicInsertList.get(i).getReportTime());
                trackInfo.setStaticShipId(staticId);
                trackList.add(trackInfo);
            }
        }

        if(!dynamicInsertList.isEmpty()){
            positionInfoMapper.batchInsertDynamicShipInfo(dynamicInsertList);
            loggerUtil.getInfoLogger().info("HQ批量插入AIS动态信息完成：{} 条", dynamicInsertList.size());
        }

        if(!dynamicUpdateList.isEmpty()){
            positionInfoMapper.batchUpdateDynamicShipInfo_byMMSI(dynamicUpdateList);
            loggerUtil.getInfoLogger().info("HQ批量更新AIS动态信息完成：{} 条", dynamicUpdateList.size());
        }

        if(!trackList.isEmpty()){
            positionInfoMapper.batchInsertHistoryTrack_HQ(trackList);
            loggerUtil.getInfoLogger().info("HQ批量插入AIS轨迹信息完成：{} 条", trackList.size());
        }
    }

    public void insertAlarmInfo(@Param("emergencyModel") EmergencyModel emergencyModel, int alarmType){
        String bdId = emergencyModel.getBasePositionModel().getTermId().toString().trim();
        String shipName = positionInfoMapper.getShipNameByBDId(bdId);
        int staticId = positionInfoMapper.selectStaticIdByBdId(bdId);
        int lon = emergencyModel.getBasePositionModel().getLongitude() * 10;
        int lat = emergencyModel.getBasePositionModel().getLatitude() * 10;
        float speed = emergencyModel.getBasePositionModel().getSpeed();
        speed = (float) (speed * 0.1 * 3600/1852);
        speed = Float.parseFloat(String.format("%.2f", speed));
        int cog = emergencyModel.getBasePositionModel().getAzimuth()*2;
        long posTime = Integer.parseInt(emergencyModel.getBasePositionModel().getPosTime());

        loggerUtil.getInfoLogger().info("ALARM:postime:"+posTime+",trans:"+Utils.GetStringTimeFromLong(posTime)+",now:"+Utils.GetNowTimeString());

        ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
        shipDynamicInfo.setSHIPNAME(shipName);
        shipDynamicInfo.setStaticShipId(staticId);
        shipDynamicInfo.setBDID(bdId);
        shipDynamicInfo.setLON(lon);
        shipDynamicInfo.setLAT(lat);
        shipDynamicInfo.setSPEED(speed);
        shipDynamicInfo.setCOG(cog);
        shipDynamicInfo.setReportTime((int)posTime);
        shipDynamicInfo.setLoadTime(Utils.GetStringTimeFromLong(posTime));
        if(alarmType == 102)
            positionInfoMapper.InsertOneAlarmInfo(shipDynamicInfo, 101, "北斗紧急报警");
        else if(alarmType == 114)
            positionInfoMapper.InsertOneAlarmInfo(shipDynamicInfo, 305, "拆卸报警");
    }

    public List<BdMsg> getBdMsg() {
        return positionInfoMapper.getBdMsg();
    }

    public void insertBDLastPos(PositionInfoModel positionInfoModel) {
        if(positionInfoModel.getBasePositionModel().getTermId().contains("283738"))
            loggerUtil.getInfoLogger().info("=======================");
    }

    public void insertShip(){
        List<String> shipList = new ArrayList<>();
        String ids = "1026535,1026550";
        shipList = Arrays.asList(ids.split(","));
        for (String shipIds:shipList){
            int shipId = Integer.parseInt(shipIds);
            int staticCount = positionInfoMapper.selectStaticInfoByShipId(shipId);
            if(staticCount > 0){

            }else{

                ShipStaticInfo shipStaticInfo = new ShipStaticInfo();
                ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
                shipStaticInfo.setSHIPID(shipId);
                String tmBdId = "";
                ShipStaticInfo ship = positionInfoMapper.getFisherByShipId(shipId);
                if (ship == null) return;
                shipStaticInfo.setBOutside(0);//0=本市
                shipStaticInfo.setSHIPNAME(ship.getSHIPNAME());
                shipStaticInfo.setOwner(ship.getOwner());
                shipStaticInfo.setSHIPTYPE(2);

                List<Bdid> bdids = positionInfoMapper.selectTermBdIdByShipId(shipId);
                for (Bdid bdid: bdids){
                    if(!tmBdId.contains(bdid.getBdid())){
                        tmBdId += bdid.getBdid() + ",";
                    }
                }

                try {
                    List<String> mmsi = positionInfoMapper.selectMmsiByShipId(shipId);
                    if (mmsi.size() > 0){
                        shipStaticInfo.setMMSI(Integer.parseInt(mmsi.get(0)));
                        shipDynamicInfo.setMMSI(Integer.parseInt(mmsi.get(0)));
                    }

                }catch (Exception e){

                }

                if(tmBdId != "" && tmBdId.substring(tmBdId.length()-1).equals(","))
                    tmBdId = tmBdId.substring(0,tmBdId.length() - 1);
                shipStaticInfo.setBDID(tmBdId);
                shipDynamicInfo.setBDID(tmBdId);

                shipDynamicInfo.setSHIPNAME(shipStaticInfo.getSHIPNAME());
                shipDynamicInfo.setSHIPTYPE(shipStaticInfo.getSHIPTYPE());
                //shipDynamicInfo.setLON(lon);
                //shipDynamicInfo.setLAT(lat);
                //shipDynamicInfo.setSPEED(speed);
                //shipDynamicInfo.setCOG(cog);
                //shipDynamicInfo.setReportTime(Integer.parseInt(posTime));

                positionInfoMapper.insertStaticShipInfo(shipStaticInfo);
                loggerUtil.getInfoLogger().info("静态信息插入---完成");
                int staticId = positionInfoMapper.selectStaticIdByShipId(shipId);
                shipDynamicInfo.setStaticShipId(staticId);
                positionInfoMapper.insertDynamicShipInfo(shipDynamicInfo);
                loggerUtil.getInfoLogger().info("动态信息插入---完成");
            }
        }
    }

    public void insertShip2(){

        //ShipStaticInfo shipStaticInfo = new ShipStaticInfo();
        //shipStaticInfo.setSHIPNAME("浙路渔88858");
        //shipStaticInfo.setBDID("415584");
        //shipStaticInfo.setBOutside(1);


        ShipDynamicInfo shipDynamicInfo = new ShipDynamicInfo();
        shipDynamicInfo.setBDID("浙路渔88858");
        shipDynamicInfo.setSHIPNAME("415584");



        //positionInfoMapper.insertStaticShipInfo(shipStaticInfo);
        //loggerUtil.getInfoLogger().info("HQ静态信息插入---完成");
        int staticId = positionInfoMapper.selectStaticIdByBdId("415584");
        shipDynamicInfo.setStaticShipId(staticId);
        positionInfoMapper.insertDynamicShipInfo(shipDynamicInfo);
        loggerUtil.getInfoLogger().info("HQ动态信息插入---完成");
    }

    //米
    public int distByToPoint(int Lon1, int Lat1, int Lon2, int Lat2){
        double aLon1 = (double) Lon1 / 180 / 10000000 * PI;
        double aLat1 = (double) Lat1 / 180 / 10000000 * PI;
        double aLon2 = (double) Lon2 / 180 / 10000000 * PI;
        double aLat2 = (double) Lat2 / 180 / 10000000 * PI;

        double dlon = aLon2 - aLon1;
        double dlat = aLat2 - aLat1;

        double a = pow(sin(dlat / 2), 2) + cos(aLat1) * cos(aLat2) * pow(sin(dlon/2), 2);
        double c = 2 * atan2(sqrt(a), sqrt(1-a) );

        double d = 6378137 * c;
        return (int) d;
    }

}



