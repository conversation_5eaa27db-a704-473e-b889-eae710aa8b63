<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="00917eb2-c0f1-4785-af6f-756e04cc2fdf" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOC<PERSON>_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../../apache-maven-3.8.5/.m2/repository/org/mybatis/mybatis-spring/1.3.2/mybatis-spring-1.3.2.jar!/org/mybatis/spring/SqlSessionTemplate.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\apache-maven-3.8.5\.m2\repository" />
        <option name="userSettingsFile" value="D:\apache-maven-3.8.5\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="272akDo36ufHcIxApF8Aia8cHyq" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/code/yimasoft/ycyg_vue_h5/server/dataAccessToDb",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\beidou\dataAccessToDb\src\main\resources\mapper" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.bdstar.data.util" />
      <recent name="com.bdstar.data.model" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn spring-boot:repackage" />
      <command value="mvn install" />
      <command value="mvn clean" />
      <command value="mvn --builder" />
      <command value="mvn package" />
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.DataAccessToDbApplication">
    <configuration default="true" type="ArquillianTestNG" factoryName="" nameIsGenerated="true">
      <option name="arquillianRunConfiguration">
        <value>
          <option name="containerStateName" value="" />
        </value>
      </option>
      <option name="TEST_OBJECT" value="CLASS" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DMDBUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.bdstar.data.util.DMDBUtils" />
      <module name="dataAccessToDb" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bdstar.data.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DataSubscriber" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.bdstar.data.subscriber.DataSubscriber" />
      <module name="dataAccessToDb" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bdstar.data.subscriber.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="PositionInfoService.s" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="dataAccessToDb" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bdstar.data.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.bdstar.data.service" />
      <option name="MAIN_CLASS_NAME" value="com.bdstar.data.service.PositionInfoService" />
      <option name="METHOD_NAME" value="s" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TestCase.test" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="dataAccessToDb" />
      <option name="PACKAGE_NAME" value="" />
      <option name="MAIN_CLASS_NAME" value="TestCase" />
      <option name="METHOD_NAME" value="test" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DataAccessToDbApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="dataAccessToDb" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bdstar.data.DataAccessToDbApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.TestCase.test" />
        <item itemvalue="JUnit.PositionInfoService.s" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="00917eb2-c0f1-4785-af6f-756e04cc2fdf" name="Default Changelist" comment="" />
      <created>1648520777989</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1648520777989</updated>
      <workItem from="1648520784899" duration="4113000" />
      <workItem from="1648524923494" duration="1928000" />
      <workItem from="1648527487286" duration="8780000" />
      <workItem from="1648538679698" duration="260000" />
      <workItem from="1648602757842" duration="57000" />
      <workItem from="1648604405097" duration="1764000" />
      <workItem from="1648618590665" duration="109000" />
      <workItem from="1648619573767" duration="642000" />
      <workItem from="1648620280502" duration="692000" />
      <workItem from="1648622886662" duration="25000" />
      <workItem from="1648630064440" duration="7000" />
      <workItem from="1648630083487" duration="19000" />
      <workItem from="1648631402731" duration="371000" />
      <workItem from="1648640963972" duration="602000" />
      <workItem from="1648646887369" duration="440000" />
      <workItem from="1648660304350" duration="5384000" />
      <workItem from="1648698223110" duration="2435000" />
      <workItem from="1648792396818" duration="804000" />
      <workItem from="1649391879201" duration="756000" />
      <workItem from="1649726628246" duration="19313000" />
      <workItem from="1649829255705" duration="1951000" />
      <workItem from="1649832271178" duration="435000" />
      <workItem from="1649833966343" duration="30749000" />
      <workItem from="1649989685144" duration="1815000" />
      <workItem from="1650017453058" duration="1225000" />
      <workItem from="1650071664561" duration="1930000" />
      <workItem from="1650087091854" duration="944000" />
      <workItem from="1650195389800" duration="2665000" />
      <workItem from="1650354613360" duration="8392000" />
      <workItem from="1650530180939" duration="827000" />
      <workItem from="1650862979425" duration="2684000" />
      <workItem from="1650897331792" duration="27528000" />
      <workItem from="1651048182369" duration="1210000" />
      <workItem from="1651082538694" duration="128000" />
      <workItem from="1651113747159" duration="1232000" />
      <workItem from="1651145007197" duration="1542000" />
      <workItem from="1651282667994" duration="1805000" />
      <workItem from="1651491051743" duration="1370000" />
      <workItem from="1651646127495" duration="483000" />
      <workItem from="1651646732872" duration="945000" />
      <workItem from="1651925997822" duration="601000" />
      <workItem from="1653116673809" duration="1278000" />
      <workItem from="1653543335895" duration="709000" />
      <workItem from="1654134311547" duration="676000" />
      <workItem from="1654571674930" duration="7556000" />
      <workItem from="1654928606923" duration="6301000" />
      <workItem from="1655082168765" duration="30000" />
      <workItem from="1655362544645" duration="619000" />
      <workItem from="1656038407197" duration="8646000" />
      <workItem from="1656335267887" duration="630000" />
      <workItem from="1656396409652" duration="356000" />
      <workItem from="1656397996862" duration="993000" />
      <workItem from="1656467685912" duration="4296000" />
      <workItem from="1657160244922" duration="2696000" />
      <workItem from="1659276869653" duration="3755000" />
      <workItem from="1659671711948" duration="5582000" />
      <workItem from="1659755851644" duration="6297000" />
      <workItem from="1660274683777" duration="819000" />
      <workItem from="1660532010944" duration="1129000" />
      <workItem from="1660703841769" duration="5686000" />
      <workItem from="1660829656007" duration="34000" />
      <workItem from="1660829699468" duration="1491000" />
      <workItem from="1660832559156" duration="26000" />
      <workItem from="1660961924373" duration="7508000" />
      <workItem from="1661048725191" duration="1927000" />
      <workItem from="1661142188555" duration="4311000" />
      <workItem from="1661243559198" duration="3759000" />
      <workItem from="1661735089639" duration="5385000" />
      <workItem from="1661740612277" duration="219000" />
      <workItem from="1661741135292" duration="8902000" />
      <workItem from="1662281384618" duration="10891000" />
      <workItem from="1662651987417" duration="854000" />
      <workItem from="1663299365821" duration="14403000" />
      <workItem from="1663727178100" duration="4863000" />
      <workItem from="1664110912500" duration="574000" />
      <workItem from="1664162201936" duration="2680000" />
      <workItem from="1665193457847" duration="12000" />
      <workItem from="1666164904592" duration="621000" />
      <workItem from="1666171291442" duration="2621000" />
      <workItem from="1666565248189" duration="1076000" />
      <workItem from="1667897600503" duration="3257000" />
      <workItem from="1668975251324" duration="1503000" />
      <workItem from="1669102158162" duration="1599000" />
      <workItem from="1669210833571" duration="4794000" />
      <workItem from="1669254102022" duration="1337000" />
      <workItem from="1669258060904" duration="594000" />
      <workItem from="1669615015701" duration="594000" />
      <workItem from="1669864403768" duration="1569000" />
      <workItem from="1669867294026" duration="1193000" />
      <workItem from="1670350174463" duration="3771000" />
      <workItem from="1670489406452" duration="913000" />
      <workItem from="1670826001605" duration="6044000" />
      <workItem from="1671265339223" duration="1989000" />
      <workItem from="1671424457332" duration="1520000" />
      <workItem from="1671760234393" duration="16000" />
      <workItem from="1671777309975" duration="1794000" />
      <workItem from="1676010174612" duration="3541000" />
      <workItem from="1676019417712" duration="607000" />
      <workItem from="1677035528155" duration="55000" />
      <workItem from="1677065489459" duration="1202000" />
      <workItem from="1677092522833" duration="901000" />
      <workItem from="1677459513427" duration="200000" />
      <workItem from="1677650609574" duration="723000" />
      <workItem from="1677693355325" duration="31000" />
      <workItem from="1678267467020" duration="611000" />
      <workItem from="1678346847667" duration="2023000" />
      <workItem from="1678670392664" duration="3192000" />
      <workItem from="1679450457743" duration="1758000" />
      <workItem from="1679540733874" duration="2049000" />
      <workItem from="1679881334592" duration="10000" />
      <workItem from="1680453304063" duration="7964000" />
      <workItem from="1680479662630" duration="9000" />
      <workItem from="1680508788185" duration="827000" />
      <workItem from="1681190295968" duration="29000" />
      <workItem from="1681622721995" duration="242000" />
      <workItem from="1681924336299" duration="1358000" />
      <workItem from="1681929240803" duration="5000" />
      <workItem from="1681980515343" duration="1805000" />
      <workItem from="1684821901319" duration="62000" />
      <workItem from="1686109335640" duration="24000" />
      <workItem from="1686126289667" duration="10724000" />
      <workItem from="1686204494107" duration="4669000" />
      <workItem from="1686293155845" duration="24000" />
      <workItem from="1689574164260" duration="7298000" />
      <workItem from="1689650542462" duration="609000" />
      <workItem from="1689663096382" duration="6000" />
      <workItem from="1689919437643" duration="2586000" />
      <workItem from="1692927282435" duration="295000" />
      <workItem from="1693537352126" duration="2651000" />
      <workItem from="1697183948873" duration="7448000" />
      <workItem from="1697597615958" duration="6467000" />
      <workItem from="1697680992334" duration="8674000" />
      <workItem from="1698350875428" duration="55000" />
      <workItem from="1698353035219" duration="286000" />
      <workItem from="1700027890066" duration="1284000" />
      <workItem from="1701174928525" duration="7000" />
      <workItem from="1702271510980" duration="1488000" />
      <workItem from="1703137854977" duration="543000" />
      <workItem from="1703139157358" duration="994000" />
      <workItem from="1706493637116" duration="201000" />
      <workItem from="1706495908865" duration="637000" />
      <workItem from="1708569618178" duration="2180000" />
      <workItem from="1709021334032" duration="837000" />
      <workItem from="1709101672223" duration="114000" />
      <workItem from="1709274522489" duration="24000" />
      <workItem from="1709534681318" duration="378000" />
      <workItem from="1710758022808" duration="2151000" />
      <workItem from="1710993403199" duration="680000" />
      <workItem from="1711087940843" duration="233000" />
      <workItem from="1711089939037" duration="261000" />
      <workItem from="1711614001429" duration="1252000" />
      <workItem from="1711681968063" duration="7000" />
      <workItem from="1712817541338" duration="168000" />
      <workItem from="1712826778604" duration="471000" />
      <workItem from="1713205466309" duration="17000" />
      <workItem from="1719297571070" duration="65000" />
      <workItem from="1720679335171" duration="8000" />
      <workItem from="1725970324199" duration="1000" />
      <workItem from="1727081906591" duration="46000" />
      <workItem from="1730697670273" duration="687000" />
      <workItem from="1731413951167" duration="1823000" />
      <workItem from="1733368871245" duration="46000" />
      <workItem from="1735015635290" duration="99000" />
      <workItem from="1742468944877" duration="647000" />
      <workItem from="1753414516268" duration="679000" />
      <workItem from="1753669309741" duration="1392000" />
      <workItem from="1753926156430" duration="1047000" />
      <workItem from="1753929709228" duration="7492000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>