package com.bdstar.data.mapper;

import com.bdstar.data.model.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IPositionInfoMapper {

    Integer insertPositionInfo(@Param("positionInfoModel") PositionInfoModel positionInfoModel,
                               @Param("posTime") String posTime);

    void updateLastPosition(@Param("positionInfoModel") PositionInfoModel positionInfoModel,
                            @Param("posTime") String posTime);

    //插入4.12
    void insertBDinfo(@Param("positionInfoModel") PositionInfoModel positionInfoModel,
                      @Param("posTime") String posTime);

    //判断终端号在静态表有无
    int selectStaticInfo(@Param("BDID") String BDID);
    int selectStaticInfoByMMSI(@Param("MMSI") int MMSI);

    //判断是否对应shipid
    int selectshipidfromFisher(@Param("BDID") String BDID);
    //
    List<ShipStaticInfo_fisher> selectFisherByBdId(@Param("BDID") String BDID);
    //查询shipid 对应的所有信息fisher
    List<ShipStaticInfo_fisher> selectFisherByShipId(@Param("shipId") int shipId);

    List<Bdid> selectFisherBdIdByShipId(@Param("shipId") int shipId);
    List<Bdid> selectTermBdIdByShipId(@Param("shipId") int shipId);

    //插入静态表
    void insertStaticShipInfo(@Param("shipStaticInfo") ShipStaticInfo shipStaticInfo);
    //插入动态表
    void insertDynamicShipInfo(@Param("shipDynamicInfo") ShipDynamicInfo shipDynamicInfo);

    //从静态表查id
    int selectStaticIdByBdId(@Param("BDID") String BDID);
    int selectStaticIdByMMSI(@Param("MMSI") int mmsi);

    //从 term表 查mmsi
    List<String> selectMmsiByShipId(@Param("shipId") int shipId);

    //
    void updateDynamicShipInfo_byBDId(@Param("shipDynamicInfo") ShipDynamicInfo shipDynamicInfo);
    void updateDynamicShipInfo_byMMSI(@Param("shipDynamicInfo") ShipDynamicInfo shipDynamicInfo);

    //轨迹
    void insertHistoryTrack(@Param("shipDynamicInfo") ShipDynamicInfo shipDynamicInfo);
    void insertHistoryTrack_HQ(@Param("shipDynamicInfo") ShipDynamicInfo shipDynamicInfo);

    // shipname
    String getAISShipNameByMMSI(@Param("mmsi")int mmsi);
    String getShipNameByBDId(@Param("bdId")String bdId);


    void InsertOneAlarmInfo(@Param("shipDynamicInfo")ShipDynamicInfo shipDynamicInfo, @Param("type")int type, @Param("msg")String msg);

    List<BdMsg> getBdMsg();

    ShipStaticInfo getFisherByShipId(@Param("shipId")int shipId);

    int selectStaticIdByShipId(@Param("shipId")int shipId);

    int selectStaticInfoByShipId(@Param("shipId")int shipId);

    int getTyphoonAlarm(@Param("shipDynamicInfo")ShipDynamicInfo shipDynamicInfo, @Param("time")String time);

    void UpdateOneAlarmInfo(@Param("shipDynamicInfo")ShipDynamicInfo shipDynamicInfo, @Param("time")String time);

    // 批量操作方法 - HQ数据处理优化
    void batchInsertStaticShipInfo(@Param("list") List<ShipStaticInfo> shipStaticInfoList);
    void batchInsertDynamicShipInfo(@Param("list") List<ShipDynamicInfo> shipDynamicInfoList);
    void batchUpdateDynamicShipInfo_byBDId(@Param("list") List<ShipDynamicInfo> shipDynamicInfoList);
    void batchUpdateDynamicShipInfo_byMMSI(@Param("list") List<ShipDynamicInfo> shipDynamicInfoList);
    void batchInsertHistoryTrack_HQ(@Param("list") List<ShipDynamicInfo> shipDynamicInfoList);

    // 批量查询静态ID
    List<Integer> batchSelectStaticIdByBdId(@Param("bdIdList") List<String> bdIdList);
    List<Integer> batchSelectStaticIdByMMSI(@Param("mmsiList") List<Integer> mmsiList);
}
