# Created at 2025-07-31T14:26:00.790
TestSet has not finished before stream error has appeared >> initializing exit by non-null configuration: null
java.io.EOFException
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at org.apache.maven.surefire.booter.MasterProcessCommand.decode(MasterProcessCommand.java:115)
	at org.apache.maven.surefire.booter.CommandReader$CommandRunnable.run(CommandReader.java:391)
	at java.lang.Thread.run(Thread.java:748)


# Created at 2025-07-31T14:26:04.252
Unexpected IOException with stream: 2025-07-31 14:26:04.248 [1;33mmain[0;39m [1;31mERROR[0;39m HikariPool-1 - Exception during pool initialization.
dm.jdbc.driver.DMException: 网络通信异常
	at dm.jdbc.driver.DBError.throwException(DBError.java:705)
	at dm.jdbc.a.a.init(DBAccess.java:1080)
	at dm.jdbc.a.a.<init>(DBAccess.java:147)
	at dm.jdbc.driver.DmdbConnection.openConnection(DmdbConnection.java:631)
	at dm.jdbc.driver.DmDriver.do_connect(DmDriver.java:129)
	at dm.jdbc.driver.DmDriver.connect(DmDriver.java:399)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:68)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:338)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:84)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:83)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	at com.sun.proxy.$Proxy75.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:166)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:83)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy83.selectStaticInfo(Unknown Source)
	at com.bdstar.data.service.PositionInfoService.processBatchBdData(PositionInfoService.java:288)
	at com.bdstar.data.service.PositionInfoService.batchUpdateShipInfo_HQ(PositionInfoService.java:261)
	at com.bdstar.data.service.PositionInfoServiceTest.testPerformanceComparison(PositionInfoServiceTest.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:688)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:210)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:206)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:131)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:65)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at dm.jdbc.a.a.init(DBAccess.java:1069)
	... 104 common frames omitted


# Created at 2025-07-31T14:26:04.270
Unexpected IOException: 7,1,com.bdstar.data.service.PositionInfoServiceTest,testPerformanceComparison,null,nested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfo\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38,null,nested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfo\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38,PositionInfoServiceTest.testPerformanceComparison:76 \00BB MyBatisSystem nested ex...,org.mybatis.spring.MyBatisSystemException: \000Anested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfo\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testPerformanceComparison(PositionInfoServiceTest.java:76)\000ACaused by: org.apache.ibatis.exceptions.PersistenceException: \000A\000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfo\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testPerformanceComparison(PositionInfoServiceTest.java:76)\000ACaused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testPerformanceComparison(PositionInfoServiceTest.java:76)\000ACaused by: dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testPerformanceComparison(PositionInfoServiceTest.java:76)\000ACaused by: java.net.SocketTimeoutException: connect timed out\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testPerformanceComparison(PositionInfoServiceTest.java:76)\000A


# Created at 2025-07-31T14:26:04.272
Unexpected IOException: 5,1,com.bdstar.data.service.PositionInfoServiceTest,testSingleUpdateShipInfo_HQ,null,null,null


# Created at 2025-07-31T14:26:04.278
Unexpected IOException with stream: 2025-07-31 14:26:04.277 [1;33mmain[0;39m [34mINFO [0;39m HikariPool-1 - Starting...


# Created at 2025-07-31T14:26:10.298
Unexpected IOException with stream: 2025-07-31 14:26:10.296 [1;33mmain[0;39m [1;31mERROR[0;39m HikariPool-1 - Exception during pool initialization.
dm.jdbc.driver.DMException: 网络通信异常
	at dm.jdbc.driver.DBError.throwException(DBError.java:705)
	at dm.jdbc.a.a.init(DBAccess.java:1080)
	at dm.jdbc.a.a.<init>(DBAccess.java:147)
	at dm.jdbc.driver.DmdbConnection.openConnection(DmdbConnection.java:631)
	at dm.jdbc.driver.DmDriver.do_connect(DmDriver.java:129)
	at dm.jdbc.driver.DmDriver.connect(DmDriver.java:399)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:68)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:338)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:84)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:83)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	at com.sun.proxy.$Proxy75.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:166)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:83)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy83.selectStaticInfoByMMSI(Unknown Source)
	at com.bdstar.data.service.PositionInfoService.processBatchAisData(PositionInfoService.java:408)
	at com.bdstar.data.service.PositionInfoService.batchUpdateShipInfo_HQ(PositionInfoService.java:266)
	at com.bdstar.data.service.PositionInfoService.updateShipInfo_HQ(PositionInfoService.java:226)
	at com.bdstar.data.service.PositionInfoServiceTest.testSingleUpdateShipInfo_HQ(PositionInfoServiceTest.java:33)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:688)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:210)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:206)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:131)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:65)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at dm.jdbc.a.a.init(DBAccess.java:1069)
	... 105 common frames omitted


# Created at 2025-07-31T14:26:10.305
Unexpected IOException: 7,1,com.bdstar.data.service.PositionInfoServiceTest,testSingleUpdateShipInfo_HQ,null,nested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfoByMMSI\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38,null,nested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfoByMMSI\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38,PositionInfoServiceTest.testSingleUpdateShipInfo_HQ:33 \00BB MyBatisSystem nested ...,org.mybatis.spring.MyBatisSystemException: \000Anested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfoByMMSI\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testSingleUpdateShipInfo_HQ(PositionInfoServiceTest.java:33)\000ACaused by: org.apache.ibatis.exceptions.PersistenceException: \000A\000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfoByMMSI\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testSingleUpdateShipInfo_HQ(PositionInfoServiceTest.java:33)\000ACaused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testSingleUpdateShipInfo_HQ(PositionInfoServiceTest.java:33)\000ACaused by: dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testSingleUpdateShipInfo_HQ(PositionInfoServiceTest.java:33)\000ACaused by: java.net.SocketTimeoutException: connect timed out\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testSingleUpdateShipInfo_HQ(PositionInfoServiceTest.java:33)\000A


# Created at 2025-07-31T14:26:10.306
Unexpected IOException: 5,1,com.bdstar.data.service.PositionInfoServiceTest,testBatchUpdateShipInfo_HQ,null,null,null


# Created at 2025-07-31T14:26:10.314
Unexpected IOException with stream: 2025-07-31 14:26:10.313 [1;33mmain[0;39m [34mINFO [0;39m HikariPool-1 - Starting...


# Created at 2025-07-31T14:26:16.340
Unexpected IOException with stream: 2025-07-31 14:26:16.337 [1;33mmain[0;39m [1;31mERROR[0;39m HikariPool-1 - Exception during pool initialization.
dm.jdbc.driver.DMException: 网络通信异常
	at dm.jdbc.driver.DBError.throwException(DBError.java:705)
	at dm.jdbc.a.a.init(DBAccess.java:1080)
	at dm.jdbc.a.a.<init>(DBAccess.java:147)
	at dm.jdbc.driver.DmdbConnection.openConnection(DmdbConnection.java:631)
	at dm.jdbc.driver.DmDriver.do_connect(DmDriver.java:129)
	at dm.jdbc.driver.DmDriver.connect(DmDriver.java:399)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:364)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:206)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:476)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:115)
	at com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:159)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:117)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:68)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:338)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:84)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:62)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:326)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:83)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:148)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:141)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:77)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:433)
	at com.sun.proxy.$Proxy75.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:166)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:83)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:59)
	at com.sun.proxy.$Proxy83.selectStaticInfo(Unknown Source)
	at com.bdstar.data.service.PositionInfoService.processBatchBdData(PositionInfoService.java:288)
	at com.bdstar.data.service.PositionInfoService.batchUpdateShipInfo_HQ(PositionInfoService.java:261)
	at com.bdstar.data.service.PositionInfoServiceTest.testBatchUpdateShipInfo_HQ(PositionInfoServiceTest.java:50)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:688)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:210)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:206)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:131)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:65)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at java.util.ArrayList.forEach(ArrayList.java:1257)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:143)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:129)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:127)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:126)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:84)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:85)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:606)
	at dm.jdbc.a.a.init(DBAccess.java:1069)
	... 104 common frames omitted


# Created at 2025-07-31T14:26:16.349
Unexpected IOException: 7,1,com.bdstar.data.service.PositionInfoServiceTest,testBatchUpdateShipInfo_HQ,null,nested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfo\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38,null,nested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfo\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38,PositionInfoServiceTest.testBatchUpdateShipInfo_HQ:50 \00BB MyBatisSystem nested e...,org.mybatis.spring.MyBatisSystemException: \000Anested exception is org.apache.ibatis.exceptions.PersistenceException: \000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfo\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testBatchUpdateShipInfo_HQ(PositionInfoServiceTest.java:50)\000ACaused by: org.apache.ibatis.exceptions.PersistenceException: \000A\000D\000A### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000D\000A### The error may exist in file [D:\005Ccode\005Cyimasoft\005Cycyg_vue_h5\005Cserver\005CdataAccessToDb\005Ctarget\005Cclasses\005Cmapper\005CPositionInfoMapper.xml]\000D\000A### The error may involve com.bdstar.data.mapper.IPositionInfoMapper.selectStaticInfo\000D\000A### The error occurred while executing a query\000D\000A### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testBatchUpdateShipInfo_HQ(PositionInfoServiceTest.java:50)\000ACaused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testBatchUpdateShipInfo_HQ(PositionInfoServiceTest.java:50)\000ACaused by: dm.jdbc.driver.DMException: \7F51\7EDC\901A\4FE1\5F02\5E38\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testBatchUpdateShipInfo_HQ(PositionInfoServiceTest.java:50)\000ACaused by: java.net.SocketTimeoutException: connect timed out\000A\0009at com.bdstar.data.service.PositionInfoServiceTest.testBatchUpdateShipInfo_HQ(PositionInfoServiceTest.java:50)\000A


# Created at 2025-07-31T14:26:16.354
Unexpected IOException: 2,1,org.apache.maven.surefire.junitplatform.JUnitPlatformProvider,com.bdstar.data.service.PositionInfoServiceTest,null,null,null


