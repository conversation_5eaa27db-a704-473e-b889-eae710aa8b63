<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5df1ccbf-01f5-41af-9cf6-41bf580ec83f" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="FxmlFile" />
        <option value="spring-beans.schema" />
        <option value="Mybatis Mapper" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../../apache-maven-3.8.5/repository/org/springframework/boot/spring-boot-autoconfigure/2.0.3.RELEASE/spring-boot-autoconfigure-2.0.3.RELEASE.jar!/org/springframework/boot/autoconfigure/SpringBootApplication.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/src/main/java/Application.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/controller/Application.java" />
        <option value="$PROJECT_DIR$/src/main/resources/application.properties" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/Application.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/service/impl/CityServiceImpl.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/mapper/departments.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/entity/Departments.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/mapper/DepartmentMapper.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/service/DeptService.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/service/CityService.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/controller/TestController.java" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/service/impl/DeptServiceImpl.java" />
        <option value="$PROJECT_DIR$/src/main/resources/application.yml" />
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/entity/City.java" />
        <option value="$PROJECT_DIR$/src/main/resources/mapper/CityMapper.xml" />
        <option value="$PROJECT_DIR$/src/main/java/com/wzt/mapper/CityMapper.java" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\develop\apache-maven-3.9.4\mvn_repo" />
        <option name="userSettingsFile" value="D:\develop\apache-maven-3.9.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="downloadAnnotationsAutomatically" value="true" />
        <option name="downloadSourcesAutomatically" value="true" />
        <option name="jdkForImporter" value="1.8" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenProjectNavigator">
    <treeState>
      <expand>
        <path>
          <item name="" type="16c1761:MavenProjectsStructure$RootNode" />
          <item name="spring-mybatis-dmdb" type="9519ce18:MavenProjectsStructure$ProjectNode" />
        </path>
      </expand>
      <select />
    </treeState>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="260" />
    <option name="y" value="20" />
    <option name="width" value="1400" />
    <option name="height" value="1000" />
  </component>
  <component name="ProjectId" id="275q4kK6VahgkjW2rXVjJ3gNDsg" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="wzt" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="wzt" type="462c0819:PsiDirectoryNode" />
              <item name="controller" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="wzt" type="462c0819:PsiDirectoryNode" />
              <item name="entity" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="wzt" type="462c0819:PsiDirectoryNode" />
              <item name="mapper" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="wzt" type="462c0819:PsiDirectoryNode" />
              <item name="service" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="wzt" type="462c0819:PsiDirectoryNode" />
              <item name="service" type="462c0819:PsiDirectoryNode" />
              <item name="impl" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="resources" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="springmybatisdmdb" type="b2602c69:ProjectViewProjectNode" />
              <item name="springmybatisdmdb" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="resources" type="462c0819:PsiDirectoryNode" />
              <item name="mapper" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="PackagesPane" />
    </panes>
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "Spring Boot.Application.executor": "Run",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/code/yimasoft/ycyg_vue_h5/server/springbootmybatisdmdb/src/lib",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "项目",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "vue.rearranger.settings.migration": "true",
    "应用程序.Application.executor": "Debug"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "dm.jdbc.driver.DmDriver"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\code\yimasoft\ycyg_vue_h5\server\springbootmybatisdmdb\src\lib" />
      <recent name="G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\src\lib" />
      <recent name="C:\Users\<USER>\Desktop\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\src\main\resources" />
      <recent name="G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\src\main\resources\mapper" />
      <recent name="G:\yimaWorking\bdxt_shipMonitor_web\Server\springbootmybatisdmdb\src\main\java\com\bd\entity" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="" />
      <recent name="com.bd.mapper" />
      <recent name="com.bd" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.bd.util" />
      <recent name="com.bd.thread" />
      <recent name="com.bd.entity" />
      <recent name="com.bd.mapper" />
      <recent name="com.bd.service.impl" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn  clean install" />
      <command value="mvn spring-boot:repackage" />
      <command value="mvn package" />
      <command value="mvn clean" />
    </option>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.Application">
    <configuration name="Application" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.bd.Application" />
      <module name="spring-mybatis-dmdb" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bd.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MPTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="spring-mybatis-dmdb" />
      <option name="PACKAGE_NAME" value="" />
      <option name="MAIN_CLASS_NAME" value="MPTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MPTest.test" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="spring-mybatis-dmdb" />
      <option name="PACKAGE_NAME" value="" />
      <option name="MAIN_CLASS_NAME" value="MPTest" />
      <option name="METHOD_NAME" value="test" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MyTest.test" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="spring-mybatis-dmdb" />
      <option name="PACKAGE_NAME" value="" />
      <option name="MAIN_CLASS_NAME" value="MyTest" />
      <option name="METHOD_NAME" value="test" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="NormalTest.test1" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="spring-mybatis-dmdb" />
      <option name="PACKAGE_NAME" value="" />
      <option name="MAIN_CLASS_NAME" value="NormalTest" />
      <option name="METHOD_NAME" value="test1" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="spring-mybatis-dmdb" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bd.Application" />
      <option name="UPDATE_ACTION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="springbootmybatisdmdb" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.bd.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bd.Application" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="JUnit.MPTest" />
      <item itemvalue="JUnit.MPTest.test" />
      <item itemvalue="JUnit.MyTest.test" />
      <item itemvalue="JUnit.NormalTest.test1" />
      <item itemvalue="Spring Boot.Application" />
      <item itemvalue="应用程序.Application" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.NormalTest.test1" />
        <item itemvalue="JUnit.MyTest.test" />
        <item itemvalue="JUnit.MPTest.test" />
        <item itemvalue="JUnit.MPTest" />
        <item itemvalue="应用程序.Application" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5df1ccbf-01f5-41af-9cf6-41bf580ec83f" name="Default Changelist" comment="" />
      <created>1576375671138</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1576375671138</updated>
      <workItem from="1576375674841" duration="9297000" />
      <workItem from="1576396430949" duration="3935000" />
      <workItem from="1648620107629" duration="2091000" />
      <workItem from="1648622418802" duration="23430000" />
      <workItem from="1648652373431" duration="18026000" />
      <workItem from="1648729037064" duration="24668000" />
      <workItem from="1648826886045" duration="3179000" />
      <workItem from="1648902114539" duration="2528000" />
      <workItem from="1649129899998" duration="7712000" />
      <workItem from="1649143606548" duration="3052000" />
      <workItem from="1649147201708" duration="4568000" />
      <workItem from="1649159186231" duration="3930000" />
      <workItem from="1649211239320" duration="799000" />
      <workItem from="1649242411227" duration="2138000" />
      <workItem from="1649249106493" duration="1616000" />
      <workItem from="1649254015253" duration="1339000" />
      <workItem from="1649371687092" duration="10575000" />
      <workItem from="1649426023596" duration="592000" />
      <workItem from="1649485785794" duration="11940000" />
      <workItem from="1649644353003" duration="18121000" />
      <workItem from="1649833888330" duration="6558000" />
      <workItem from="1649916102356" duration="8205000" />
      <workItem from="1649985360322" duration="4492000" />
      <workItem from="1650025692682" duration="3136000" />
      <workItem from="1650070645055" duration="26329000" />
      <workItem from="1650173927757" duration="912000" />
      <workItem from="1650193100845" duration="7490000" />
      <workItem from="1650242326852" duration="20858000" />
      <workItem from="1650332521126" duration="1135000" />
      <workItem from="1650333947507" duration="235000" />
      <workItem from="1650353929669" duration="9075000" />
      <workItem from="1650506393478" duration="12003000" />
      <workItem from="1650775345586" duration="9233000" />
      <workItem from="1650830255978" duration="3547000" />
      <workItem from="1650897280457" duration="17860000" />
      <workItem from="1651049769227" duration="2308000" />
      <workItem from="1651082667667" duration="27116000" />
      <workItem from="1651285541578" duration="53482000" />
      <workItem from="1651800781245" duration="4187000" />
      <workItem from="1651990266963" duration="11620000" />
      <workItem from="1652100555301" duration="4104000" />
      <workItem from="1620697650986" duration="66000" />
      <workItem from="1620697748711" duration="6854000" />
      <workItem from="1652317534076" duration="1917000" />
      <workItem from="1652363433131" duration="14908000" />
      <workItem from="1652944017054" duration="23285000" />
      <workItem from="1653615578917" duration="26510000" />
      <workItem from="1654062995691" duration="50822000" />
      <workItem from="1654687609010" duration="637000" />
      <workItem from="1654688900776" duration="9822000" />
      <workItem from="1654776669525" duration="10247000" />
      <workItem from="1654911491800" duration="15065000" />
      <workItem from="1655172914545" duration="6633000" />
      <workItem from="1655256229832" duration="22994000" />
      <workItem from="1655371502847" duration="7894000" />
      <workItem from="1655455331542" duration="2588000" />
      <workItem from="1655533521515" duration="4244000" />
      <workItem from="1655729622046" duration="112000" />
      <workItem from="1655776727767" duration="10423000" />
      <workItem from="1655859506228" duration="3777000" />
      <workItem from="1655866502409" duration="6359000" />
      <workItem from="1656052027283" duration="3101000" />
      <workItem from="1656398743762" duration="5257000" />
      <workItem from="1656465733752" duration="16898000" />
      <workItem from="1656639806711" duration="8340000" />
      <workItem from="1656928085572" duration="278000" />
      <workItem from="1656995923140" duration="8939000" />
      <workItem from="1657092808656" duration="609000" />
      <workItem from="1657095695753" duration="674000" />
      <workItem from="1657160188120" duration="1472000" />
      <workItem from="1657246442950" duration="4975000" />
      <workItem from="1657261201033" duration="3311000" />
      <workItem from="1657520690595" duration="2473000" />
      <workItem from="1657606291408" duration="2134000" />
      <workItem from="1658106295424" duration="1710000" />
      <workItem from="1658224434666" duration="2231000" />
      <workItem from="1658283046059" duration="19156000" />
      <workItem from="1658453817213" duration="1294000" />
      <workItem from="1658475692832" duration="1765000" />
      <workItem from="1658499827615" duration="7157000" />
      <workItem from="1658713531874" duration="26991000" />
      <workItem from="1658825187887" duration="10746000" />
      <workItem from="1658989500837" duration="11809000" />
      <workItem from="1659232158987" duration="24740000" />
      <workItem from="1659362561735" duration="1197000" />
      <workItem from="1659410238265" duration="1589000" />
      <workItem from="1659500874566" duration="944000" />
      <workItem from="1659501850321" duration="305000" />
      <workItem from="1659503419341" duration="16029000" />
      <workItem from="1659755320528" duration="36599000" />
      <workItem from="1659888486300" duration="603000" />
      <workItem from="1659925680299" duration="6269000" />
      <workItem from="1660010863233" duration="2921000" />
      <workItem from="1660142330381" duration="527000" />
      <workItem from="1660186539217" duration="14561000" />
      <workItem from="1660220614281" duration="18226000" />
      <workItem from="1660473234894" duration="18614000" />
      <workItem from="1660614548635" duration="26260000" />
      <workItem from="1660703824094" duration="8027000" />
      <workItem from="1660786842037" duration="1034000" />
      <workItem from="1660805905004" duration="8301000" />
      <workItem from="1660834278940" duration="817000" />
      <workItem from="1661496828819" duration="7903000" />
      <workItem from="1661584305338" duration="22821000" />
      <workItem from="1661774461479" duration="47460000" />
      <workItem from="1662643625868" duration="5503000" />
      <workItem from="1662686011305" duration="130000" />
      <workItem from="1662688614677" duration="2018000" />
      <workItem from="1662694415220" duration="10246000" />
      <workItem from="1663068710195" duration="3396000" />
      <workItem from="1663215735544" duration="22312000" />
      <workItem from="1663727148474" duration="6920000" />
      <workItem from="1663838098465" duration="1317000" />
      <workItem from="1663898096965" duration="20307000" />
      <workItem from="1665193462404" duration="16162000" />
      <workItem from="1665625265167" duration="1200000" />
      <workItem from="1665711831675" duration="34793000" />
      <workItem from="1666269747296" duration="1188000" />
      <workItem from="1666317182475" duration="9816000" />
      <workItem from="1666575908398" duration="5708000" />
      <workItem from="1666661319732" duration="33270000" />
      <workItem from="1667440934542" duration="3579000" />
      <workItem from="1667477176211" duration="2291000" />
      <workItem from="1667508295965" duration="2342000" />
      <workItem from="1667809536427" duration="1788000" />
      <workItem from="1667896438875" duration="5013000" />
      <workItem from="1668074167522" duration="9029000" />
      <workItem from="1668154090115" duration="105000" />
      <workItem from="1668395326973" duration="14289000" />
      <workItem from="1668671705754" duration="85000" />
      <workItem from="1668679321187" duration="5630000" />
      <workItem from="1668754943467" duration="1312000" />
      <workItem from="1668763801470" duration="113000" />
      <workItem from="1668973446381" duration="135000" />
      <workItem from="1668975202013" duration="236000" />
      <workItem from="1669102840243" duration="8170000" />
      <workItem from="1669210001870" duration="7878000" />
      <workItem from="1669254950604" duration="311000" />
      <workItem from="1669620047781" duration="9196000" />
      <workItem from="1669862896008" duration="25656000" />
      <workItem from="1670207558020" duration="18822000" />
      <workItem from="1670393377206" duration="18771000" />
      <workItem from="1671265346770" duration="11123000" />
      <workItem from="1673424911133" duration="1904000" />
      <workItem from="1673572526925" duration="4334000" />
      <workItem from="1675227299063" duration="14788000" />
      <workItem from="1675318660492" duration="20326000" />
      <workItem from="1676016509334" duration="1273000" />
      <workItem from="1676271478596" duration="25626000" />
      <workItem from="1677092518014" duration="13046000" />
      <workItem from="1677429963990" duration="8456000" />
      <workItem from="1677459508264" duration="211000" />
      <workItem from="1677460447158" duration="101000" />
      <workItem from="1677650576701" duration="626000" />
      <workItem from="1677661138548" duration="749000" />
      <workItem from="1677689286925" duration="597000" />
      <workItem from="1677690106744" duration="6109000" />
      <workItem from="1677722067942" duration="12115000" />
      <workItem from="1677828225959" duration="9841000" />
      <workItem from="1678267457172" duration="623000" />
      <workItem from="1678346634151" duration="15399000" />
      <workItem from="1678670385355" duration="12000" />
      <workItem from="1678673551425" duration="10440000" />
      <workItem from="1678936637830" duration="24207000" />
      <workItem from="1679391228653" duration="1399000" />
      <workItem from="1679450449053" duration="3070000" />
      <workItem from="1679476723735" duration="1633000" />
      <workItem from="1679540721470" duration="17000" />
      <workItem from="1679881347371" duration="4116000" />
      <workItem from="1679985071712" duration="16000" />
      <workItem from="1680233641225" duration="30154000" />
      <workItem from="1680479674054" duration="4088000" />
      <workItem from="1680505155470" duration="3968000" />
      <workItem from="1680570426888" duration="1478000" />
      <workItem from="1680743033058" duration="882000" />
      <workItem from="1680773423922" duration="2334000" />
      <workItem from="1680860336522" duration="1302000" />
      <workItem from="1680919238868" duration="2660000" />
      <workItem from="1681190260021" duration="104000" />
      <workItem from="1681193002360" duration="7000" />
      <workItem from="1681287115625" duration="2793000" />
      <workItem from="1681349775506" duration="55419000" />
      <workItem from="1681929251102" duration="561000" />
      <workItem from="1681930048307" duration="22522000" />
      <workItem from="1682063978138" duration="29399000" />
      <workItem from="1682324142378" duration="17895000" />
      <workItem from="1682875591100" duration="1363000" />
      <workItem from="1683168878555" duration="836000" />
      <workItem from="1683185768134" duration="9272000" />
      <workItem from="1683261043033" duration="311000" />
      <workItem from="1683263482870" duration="12845000" />
      <workItem from="1683339846875" duration="3845000" />
      <workItem from="1683343899807" duration="4456000" />
      <workItem from="1683788308309" duration="4050000" />
      <workItem from="1683796888022" duration="599000" />
      <workItem from="1684305623570" duration="612000" />
      <workItem from="1684718406873" duration="3604000" />
      <workItem from="1684983371178" duration="56000" />
      <workItem from="1685429337284" duration="755000" />
      <workItem from="1685683562111" duration="1266000" />
      <workItem from="1686109313817" duration="50000" />
      <workItem from="1686121700566" duration="128000" />
      <workItem from="1686126270375" duration="28000" />
      <workItem from="1686293182386" duration="28409000" />
      <workItem from="1686801114555" duration="18632000" />
      <workItem from="1688351979539" duration="464000" />
      <workItem from="1689328954905" duration="1463000" />
      <workItem from="1689650535872" duration="193000" />
      <workItem from="1689663112593" duration="9415000" />
      <workItem from="1690181292480" duration="4514000" />
      <workItem from="1690451115683" duration="512000" />
      <workItem from="1690451719597" duration="780000" />
      <workItem from="1690452525937" duration="565000" />
      <workItem from="1690453098581" duration="30000" />
      <workItem from="1690453147288" duration="62000" />
      <workItem from="1690453226456" duration="12502000" />
      <workItem from="1690470186154" duration="315000" />
      <workItem from="1690470622325" duration="1845000" />
      <workItem from="1690472533926" duration="18623000" />
      <workItem from="1690853804522" duration="8192000" />
      <workItem from="1691654023240" duration="2657000" />
      <workItem from="1691657396966" duration="48000" />
      <workItem from="1692755848439" duration="11594000" />
      <workItem from="1693364982460" duration="12217000" />
      <workItem from="1693537334160" duration="417000" />
      <workItem from="1693554125472" duration="41955000" />
      <workItem from="1694654054350" duration="1331000" />
      <workItem from="1694664355565" duration="2965000" />
      <workItem from="1695105518620" duration="9471000" />
      <workItem from="1695175302551" duration="54835000" />
      <workItem from="1696644785152" duration="3690000" />
      <workItem from="1697183883276" duration="16071000" />
      <workItem from="1697597615958" duration="1550000" />
      <workItem from="1697680339288" duration="11468000" />
      <workItem from="1698350930531" duration="2377000" />
      <workItem from="1698717447230" duration="4764000" />
      <workItem from="1701155221184" duration="14693000" />
      <workItem from="1701272338410" duration="200000" />
      <workItem from="1701845873870" duration="3261000" />
      <workItem from="1702017673567" duration="2384000" />
      <workItem from="1703137833531" duration="19000" />
      <workItem from="1706493670691" duration="572000" />
      <workItem from="1706494440564" duration="394000" />
      <workItem from="1706495897973" duration="649000" />
      <workItem from="1708568568028" duration="3602000" />
      <workItem from="1709021319689" duration="7000" />
      <workItem from="1709534784619" duration="6187000" />
      <workItem from="1709803470481" duration="6967000" />
      <workItem from="1710757959614" duration="62000" />
      <workItem from="1710758095764" duration="7000" />
      <workItem from="1710901222801" duration="7655000" />
      <workItem from="1710993495240" duration="1667000" />
      <workItem from="1711087936324" duration="1991000" />
      <workItem from="1711614008414" duration="1193000" />
      <workItem from="1711679785276" duration="9000" />
      <workItem from="1711938914838" duration="2231000" />
      <workItem from="1712456094426" duration="919000" />
      <workItem from="1712817474464" duration="229000" />
      <workItem from="1713237625918" duration="8489000" />
      <workItem from="1713248841060" duration="521000" />
      <workItem from="1713322315765" duration="10062000" />
      <workItem from="1715857024892" duration="7329000" />
      <workItem from="1716355193003" duration="2978000" />
      <workItem from="1716794246017" duration="9000" />
      <workItem from="1719297540897" duration="62000" />
      <workItem from="1720679345217" duration="3359000" />
      <workItem from="1720698972482" duration="9685000" />
      <workItem from="1720752176650" duration="9402000" />
      <workItem from="1720764392348" duration="298000" />
      <workItem from="1720765154135" duration="2838000" />
      <workItem from="1721369622909" duration="613000" />
      <workItem from="1722401683861" duration="1653000" />
      <workItem from="1723193067533" duration="38000" />
      <workItem from="1723775433229" duration="614000" />
      <workItem from="1723983081669" duration="1578000" />
      <workItem from="1723997242676" duration="1377000" />
      <workItem from="1725245957184" duration="1591000" />
      <workItem from="1725358843227" duration="150000" />
      <workItem from="1726023173225" duration="12567000" />
      <workItem from="1726214563742" duration="14000" />
      <workItem from="1726296288135" duration="783000" />
      <workItem from="1726625733467" duration="595000" />
      <workItem from="1726656372691" duration="2408000" />
      <workItem from="1726733276577" duration="5601000" />
      <workItem from="1727009301841" duration="1347000" />
      <workItem from="1727599439500" duration="772000" />
      <workItem from="1730352876502" duration="532000" />
      <workItem from="1735197055186" duration="2766000" />
      <workItem from="1735199944158" duration="140000" />
      <workItem from="1735200125892" duration="9488000" />
      <workItem from="1735268867888" duration="7506000" />
      <workItem from="1735800538556" duration="3027000" />
      <workItem from="1735830874785" duration="1091000" />
      <workItem from="1735874524246" duration="2648000" />
      <workItem from="1735974275636" duration="1620000" />
      <workItem from="1736046334299" duration="890000" />
      <workItem from="1736148858373" duration="593000" />
      <workItem from="1736157398018" duration="1155000" />
      <workItem from="1736159199620" duration="603000" />
      <workItem from="1736216715638" duration="857000" />
      <workItem from="1737343427891" duration="1580000" />
      <workItem from="1737600297312" duration="1896000" />
      <workItem from="1737615449755" duration="17000" />
      <workItem from="1737616242007" duration="1040000" />
      <workItem from="1740373218184" duration="1578000" />
      <workItem from="1740537989069" duration="3144000" />
      <workItem from="1740718501822" duration="2789000" />
      <workItem from="1741003239989" duration="1219000" />
      <workItem from="1741163819252" duration="706000" />
      <workItem from="1741603897389" duration="9065000" />
      <workItem from="1741683567634" duration="1622000" />
      <workItem from="1742366113085" duration="5115000" />
      <workItem from="1742436140460" duration="691000" />
      <workItem from="1742458044322" duration="1372000" />
      <workItem from="1742467977743" duration="4289000" />
      <workItem from="1742520508040" duration="370000" />
      <workItem from="1742524106339" duration="884000" />
      <workItem from="1742950926078" duration="1992000" />
      <workItem from="1744944616612" duration="2794000" />
      <workItem from="1745977889993" duration="965000" />
      <workItem from="1749005402152" duration="69000" />
      <workItem from="1751248500336" duration="1523000" />
      <workItem from="1751437102405" duration="1009000" />
      <workItem from="1751439745130" duration="6880000" />
      <workItem from="1751447473573" duration="2328000" />
      <workItem from="1751449987945" duration="1473000" />
      <workItem from="1751506030781" duration="9413000" />
      <workItem from="1751523831151" duration="4916000" />
      <workItem from="1751590866468" duration="6215000" />
      <workItem from="1751850417632" duration="3970000" />
      <workItem from="1751936882601" duration="7227000" />
      <workItem from="1751956111973" duration="2153000" />
      <workItem from="1751979606956" duration="313000" />
      <workItem from="1752023244551" duration="5049000" />
      <workItem from="1752111430716" duration="7070000" />
      <workItem from="1752455368962" duration="3339000" />
      <workItem from="1752482732982" duration="1023000" />
      <workItem from="1752486465699" duration="955000" />
      <workItem from="1752542566748" duration="1248000" />
      <workItem from="1752627795679" duration="1353000" />
      <workItem from="1752670408681" duration="6527000" />
      <workItem from="1752742376703" duration="3798000" />
      <workItem from="1752800785850" duration="6200000" />
      <workItem from="1753060373606" duration="3756000" />
      <workItem from="1753091744260" duration="328000" />
      <workItem from="1753092445097" duration="88000" />
      <workItem from="1753092559094" duration="310000" />
      <workItem from="1753099225741" duration="210000" />
      <workItem from="1753146688725" duration="2642000" />
      <workItem from="1753164528658" duration="430000" />
      <workItem from="1753179399661" duration="2209000" />
      <workItem from="1753235746559" duration="1948000" />
      <workItem from="1753320398989" duration="5725000" />
      <workItem from="1753405200096" duration="4959000" />
      <workItem from="1753427235569" duration="2408000" />
      <workItem from="1753433425429" duration="4000" />
      <workItem from="1753668404404" duration="3637000" />
      <workItem from="1753754031384" duration="25494000" />
      <workItem from="1753793015098" duration="10000" />
      <workItem from="1753837519464" duration="5422000" />
      <workItem from="1753924983332" duration="3705000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="13232000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1936" height="1056" extended-state="6" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.25266525" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Designer" order="2" />
      <window_info id="Image Layers" order="3" />
      <window_info id="Capture Tool" order="4" />
      <window_info id="UI Designer" order="5" />
      <window_info id="Favorites" order="6" side_tool="true" />
      <window_info id="Web" order="7" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.3564464" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Spring" order="7" />
      <window_info anchor="bottom" id="Terminal" order="8" />
      <window_info anchor="bottom" id="Docker" order="9" show_stripe_button="false" />
      <window_info anchor="bottom" id="Event Log" order="10" side_tool="true" />
      <window_info anchor="bottom" id="Messages" order="11" weight="0.32936078" />
      <window_info anchor="bottom" id="Java Enterprise" order="12" />
      <window_info anchor="bottom" id="Database Changes" order="13" />
      <window_info anchor="bottom" id="Version Control" order="14" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Maven" order="3" visible="true" weight="0.16044776" />
      <window_info anchor="right" id="Palette" order="4" />
      <window_info anchor="right" id="Capture Analysis" order="5" />
      <window_info anchor="right" id="Database" order="6" />
      <window_info anchor="right" id="Palette&#9;" order="7" />
      <window_info anchor="right" id="Theme Preview" order="8" />
      <window_info anchor="right" id="Bean Validation" order="9" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="application.yml" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <breakpoint type="java-exception">
          <properties class="java.sql.SQLException" package="java.sql" />
          <option name="timeStamp" value="2" />
        </breakpoint>
        <breakpoint type="java-exception">
          <properties class="java.lang.NullPointerException" package="java.lang" />
          <option name="timeStamp" value="3" />
        </breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/bd/service/impl/PeopleImpl.java</url>
          <line>190</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/bd/controller/HttpController.java</url>
          <line>1137</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="JUnit">
        <watch expression="recordOf7.getCrewStr()" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/src/main/resources/application.properties" />
    <entry file="file://$PROJECT_DIR$/springmybatisdmdb.iml">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="jar://$PROJECT_DIR$/src/lib/Dm7JdbcDriver18.jar!/dm/jdbc/driver/DmDriver.class">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="150">
          <caret line="20" column="13" selection-start-line="20" selection-start-column="13" selection-end-line="20" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/Application.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="171">
          <caret line="7" column="11" selection-start-line="7" selection-start-column="11" selection-end-line="7" selection-end-column="11" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/entity/Departments.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="175">
          <caret line="7" column="32" selection-start-line="7" selection-start-column="32" selection-end-line="7" selection-end-column="32" />
          <folding>
            <element signature="e#173#174#0" expanded="true" />
            <element signature="e#219#220#0" expanded="true" />
            <element signature="e#257#258#0" expanded="true" />
            <element signature="e#293#294#0" expanded="true" />
            <element signature="e#325#326#0" expanded="true" />
            <element signature="e#375#376#0" expanded="true" />
            <element signature="e#417#418#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/service/CityService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="275">
          <caret line="11" selection-start-line="11" selection-end-line="11" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/service/DeptService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="225">
          <caret line="9" column="17" selection-start-line="9" selection-start-column="17" selection-end-line="9" selection-end-column="17" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/service/impl/CityServiceImpl.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="397">
          <caret line="25" selection-start-line="25" selection-end-line="25" />
          <folding>
            <element signature="imports" expanded="true" />
            <element signature="e#556#557#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/service/impl/DeptServiceImpl.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="169">
          <caret line="10" column="8" selection-start-line="10" selection-start-column="8" selection-end-line="10" selection-end-column="8" />
          <folding>
            <element signature="imports" expanded="true" />
            <element signature="e#238#239#0" expanded="true" />
            <element signature="e#265#266#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/resources/application.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="244">
          <caret line="10" column="37" selection-start-line="10" selection-start-column="37" selection-end-line="10" selection-end-column="37" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pom.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="300">
          <caret line="42" column="21" lean-forward="true" selection-start-line="42" selection-start-column="21" selection-end-line="42" selection-end-column="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/entity/City.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="225">
          <caret line="9" column="31" selection-start-line="9" selection-start-column="31" selection-end-line="9" selection-end-column="31" />
          <folding>
            <element signature="e#193#194#0" expanded="true" />
            <element signature="e#237#238#0" expanded="true" />
            <element signature="e#273#274#0" expanded="true" />
            <element signature="e#466#467#0" expanded="true" />
            <element signature="e#497#498#0" expanded="true" />
            <element signature="e#546#547#0" expanded="true" />
            <element signature="e#586#587#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/controller/TestController.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="294">
          <caret line="38" column="29" selection-start-line="38" selection-start-column="18" selection-end-line="38" selection-end-column="29" />
          <folding>
            <element signature="imports" expanded="true" />
            <element signature="e#732#733#0" expanded="true" />
            <element signature="e#777#778#0" expanded="true" />
            <element signature="e#975#976#0" expanded="true" />
            <element signature="e#1022#1023#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/resources/mapper/CityMapper.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="244">
          <caret line="11" lean-forward="true" selection-start-line="11" selection-end-line="11" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/mapper/CityMapper.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="275">
          <caret line="11" column="23" selection-start-line="11" selection-start-column="23" selection-end-line="11" selection-end-column="23" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/com/wzt/mapper/DepartmentMapper.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="300">
          <caret line="12" column="40" lean-forward="true" selection-start-line="12" selection-start-column="40" selection-end-line="12" selection-end-column="40" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ArtifactsStructureConfigurable.UI">
        <settings>
          <artifact-editor />
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="FacetStructureConfigurable.UI">
        <settings>
          <last-edited>Spring</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="GlobalLibrariesConfigurable.UI">
        <settings>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="JdkListConfigurable.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ModuleStructureConfigurable.UI">
        <settings>
          <last-edited>springmybatisdmdb</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
                <option value="0.6" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ProjectJDKs.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ProjectLibrariesConfigurable.UI">
        <settings>
          <last-edited>Maven: ch.qos.logback:logback-classic:1.2.3</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>