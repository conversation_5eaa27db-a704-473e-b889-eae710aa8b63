-- 性能监控SQL脚本
-- 用于监控批量操作对数据库的影响

-- 1. 查看表的记录数量变化
SELECT 
    'SHIP_STATICINFO' as table_name,
    COUNT(*) as record_count,
    MAX(LOADTIME) as last_update_time
FROM SHIP.SHIP_STATICINFO
UNION ALL
SELECT 
    'SHIP_DYNAMICTION' as table_name,
    COUNT(*) as record_count,
    MAX(LOADTIME) as last_update_time
FROM SHIP.SHIP_DYNAMICTION
UNION ALL
SELECT 
    'SHIP_HISTORYTRACKPOINT_HQ' as table_name,
    COUNT(*) as record_count,
    MAX(LOADTIME) as last_update_time
FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ;

-- 2. 查看最近插入的数据
-- 静态信息表最近插入的记录
SELECT 
    ID, SHIPNAME, BDID, MMSI, LOADTIME
FROM SHIP.SHIP_STATICINFO 
WHERE LOADTIME >= SYSDATE - INTERVAL '1' HOUR
ORDER BY LOADTIME DESC;

-- 动态信息表最近更新的记录
SELECT 
    ID, SHIPNAME, BDID, MMSI, LON, LAT, SPEED, REPORTTIME, LOADTIME
FROM SHIP.SHIP_DYNAMICTION 
WHERE LOADTIME >= SYSDATE - INTERVAL '1' HOUR
ORDER BY LOADTIME DESC;

-- 轨迹表最近插入的记录
SELECT 
    ID, STATICSHIPID, BDID, MMSI, LON, LAT, SPEED, REPORTTIME, LOADTIME
FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ 
WHERE LOADTIME >= SYSDATE - INTERVAL '1' HOUR
ORDER BY LOADTIME DESC;

-- 3. 性能分析查询
-- 查看批量操作的效果（按时间段统计）
SELECT 
    DATE_TRUNC('minute', LOADTIME) as time_minute,
    COUNT(*) as insert_count
FROM SHIP.SHIP_STATICINFO 
WHERE LOADTIME >= SYSDATE - INTERVAL '1' HOUR
GROUP BY DATE_TRUNC('minute', LOADTIME)
ORDER BY time_minute DESC;

-- 4. 数据完整性检查
-- 检查是否有孤立的动态信息（没有对应的静态信息）
SELECT 
    d.ID, d.SHIPNAME, d.BDID, d.MMSI
FROM SHIP.SHIP_DYNAMICTION d
LEFT JOIN SHIP.SHIP_STATICINFO s ON d.STATICSHIPID = s.ID
WHERE s.ID IS NULL
AND d.LOADTIME >= SYSDATE - INTERVAL '1' HOUR;

-- 检查是否有孤立的轨迹信息
SELECT 
    h.ID, h.STATICSHIPID, h.BDID, h.MMSI
FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ h
LEFT JOIN SHIP.SHIP_STATICINFO s ON h.STATICSHIPID = s.ID
WHERE s.ID IS NULL
AND h.LOADTIME >= SYSDATE - INTERVAL '1' HOUR;

-- 5. 性能指标查询
-- 查看数据库会话信息（如果有权限）
-- SELECT 
--     SID, SERIAL#, USERNAME, PROGRAM, STATUS, 
--     LOGON_TIME, LAST_CALL_ET
-- FROM V$SESSION 
-- WHERE USERNAME = 'SHIP'
-- ORDER BY LAST_CALL_ET DESC;

-- 6. 测试数据清理（测试完成后使用）
-- 清理测试数据的SQL（谨慎使用）
/*
-- 删除测试静态信息
DELETE FROM SHIP.SHIP_STATICINFO 
WHERE SHIPNAME LIKE '%测试%' 
   OR SHIPNAME LIKE '%BATCH_%' 
   OR SHIPNAME LIKE '%SINGLE_%'
   OR BDID LIKE 'BD%'
   OR MMSI BETWEEN 100000000 AND 199999999;

-- 删除测试动态信息
DELETE FROM SHIP.SHIP_DYNAMICTION 
WHERE SHIPNAME LIKE '%测试%' 
   OR SHIPNAME LIKE '%BATCH_%' 
   OR SHIPNAME LIKE '%SINGLE_%'
   OR BDID LIKE 'BD%'
   OR MMSI BETWEEN 100000000 AND 199999999;

-- 删除测试轨迹信息
DELETE FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ 
WHERE BDID LIKE 'BD%'
   OR MMSI BETWEEN 100000000 AND 199999999;

COMMIT;
*/

-- 7. 创建性能监控视图（可选）
CREATE OR REPLACE VIEW V_PERFORMANCE_SUMMARY AS
SELECT 
    'Last Hour Inserts' as metric_name,
    (SELECT COUNT(*) FROM SHIP.SHIP_STATICINFO WHERE LOADTIME >= SYSDATE - INTERVAL '1' HOUR) as static_count,
    (SELECT COUNT(*) FROM SHIP.SHIP_DYNAMICTION WHERE LOADTIME >= SYSDATE - INTERVAL '1' HOUR) as dynamic_count,
    (SELECT COUNT(*) FROM SHIP.SHIP_HISTORYTRACKPOINT_HQ WHERE LOADTIME >= SYSDATE - INTERVAL '1' HOUR) as track_count
FROM DUAL;

-- 查看性能摘要
SELECT * FROM V_PERFORMANCE_SUMMARY;
