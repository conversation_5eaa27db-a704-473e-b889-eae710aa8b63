<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bdstar.data.mapper.IPositionInfoMapper">

    <insert id="insertPositionInfo" parameterType="com.bdstar.data.model.PositionInfoModel">
        insert into SHIP.POSITION(POSID,
                             TERMTYPE,
                             TERMNO,
                             SHIPID,
                             POSTIME,
                             LONGITUDE,
                             LATITUDE,
                             SPEED,
                             AZIMUTH,
                             LOADTIME,
                             ISVALID,
                             RETPERIOD,
                             YEAR,
                             MONTH,
                             DAY)
        VALUES (0,
        #{positionInfoModel.basePositionModel.termType},
        #{positionInfoModel.basePositionModel.termId},
        #{positionInfoModel.basePositionModel.shipId},
        #{positionInfoModel.basePositionModel.posTime},
        #{positionInfoModel.basePositionModel.longitude},
        #{positionInfoModel.basePositionModel.latitude},
        #{positionInfoModel.basePositionModel.speed},
        #{positionInfoModel.basePositionModel.azimuth},
        sysdate,
        #{positionInfoModel.isValid},
        #{positionInfoModel.retperiod},
        #{positionInfoModel.year},
        #{positionInfoModel.month},
        #{positionInfoModel.day}
        )
    </insert>


    <update id="updateLastPosition" parameterType="com.bdstar.data.model.PositionInfoModel">
        update SHIP.BDTERM_LAST_POS
        set LOADTIME = sysdate,
        RPTTIME = #{positionInfoModel.basePositionModel.posTime},
        LONGITUDE = #{positionInfoModel.basePositionModel.longitude},
        LATITUDE = #{positionInfoModel.basePositionModel.latitude},
        SPEED = #{positionInfoModel.basePositionModel.speed},
        AZIMUTH = #{positionInfoModel.basePositionModel.azimuth}
        where TERMNO = #{positionInfoModel.basePositionModel.termId}
    </update>

    <insert id="insertBDinfo" parameterType="com.bdstar.data.model.PositionInfoModel">

    </insert>

    <select id="selectStaticInfo" resultType="java.lang.Integer">
        SELECT COUNT (id) from SHIP.SHIP_STATICINFO WHERE BDID LIKE concat('%',#{BDID},'%');
    </select>

    <select id="selectshipidfromFisher" resultType="java.lang.Integer">
        SELECT SHIPID from FISHER WHERE BDID = #{BDID}
    </select>

    <resultMap id="fisher" type="com.bdstar.data.model.ShipStaticInfo_fisher">
        <result column="SHIPID" property="shipId"/>
        <result column="BDID" property="bdId"/>
        <result column="SHIPNAME" property="shipName"/>
        <result column="OWNER" property="owner"/>
    </resultMap>

    <select id="selectFisherByShipId" resultType="com.bdstar.data.model.ShipStaticInfo_fisher" resultMap="fisher">
        SELECT BDID,SHIPNAME,OWNER from FISHER WHERE SHIPID = #{shipId}
    </select>

    <select id="selectFisherByBdId" resultType="com.bdstar.data.model.ShipStaticInfo_fisher" resultMap="fisher">
        SELECT BDID,SHIPNAME,OWNER from FISHER WHERE BDID = #{BDID}
    </select>

    <resultMap id="BDid" type="com.bdstar.data.model.Bdid">
        <result column="BDZDH" jdbcType="VARCHAR" property="bdid" />
    </resultMap>

    <select id="selectFisherBdIdByShipId" resultType="com.bdstar.data.model.Bdid" resultMap="BDid">
        SELECT BDZDH FROM FISHER WHERE SHIPID = #{shipId}
    </select>

    <insert id="insertStaticShipInfo">
        insert into SHIP.SHIP_STATICINFO
        (SHIPID, BDID, MMSI, SHIPNAME, IMO, CALLSIGN, OWNER, LXDH, LXDZ, CTCL, DW,
        ZJGLQW, SSDW, SHIPTYPE, LENGTH, WIDTH, BOUTSIDE, NATION, LOADTIME, BFOCUS, BBLACKFUXIU, BWHITEFUXIU)
        VALUES(
        #{shipStaticInfo.SHIPID},
        #{shipStaticInfo.BDID},
        #{shipStaticInfo.MMSI},
        #{shipStaticInfo.SHIPNAME},
        #{shipStaticInfo.IMO},
        #{shipStaticInfo.CallSign},
        #{shipStaticInfo.Owner},
        #{shipStaticInfo.LXDH},
        #{shipStaticInfo.LXDZ},
        #{shipStaticInfo.CTCL},
        #{shipStaticInfo.DW},
        #{shipStaticInfo.ZJGLQW},
        #{shipStaticInfo.SSDW},
        #{shipStaticInfo.SHIPTYPE},
        #{shipStaticInfo.length},
        #{shipStaticInfo.width},
        #{shipStaticInfo.bOutside},
        #{shipStaticInfo.nation},
        sysdate,
        #{shipStaticInfo.bFocus},
        #{shipStaticInfo.bBlackFuXiu},
        #{shipStaticInfo.bWhiteFuXiu}
        );
    </insert>

    <insert id="insertDynamicShipInfo">
        insert into SHIP.SHIP_DYNAMICTION(STATICSHIPID, BDID, MMSI, SHIPNAME, CALLSIGN, LON, LAT, SPEED, COG, ROT, SHIPTYPE, INPORTID, INPORTSTATE, REPORTTIME, LOADTIME)
        VALUES(
        #{shipDynamicInfo.StaticShipId},
        #{shipDynamicInfo.BDID},
        #{shipDynamicInfo.MMSI},
        #{shipDynamicInfo.SHIPNAME},
        #{shipDynamicInfo.callSign},
        #{shipDynamicInfo.LON},
        #{shipDynamicInfo.LAT},
        #{shipDynamicInfo.SPEED},
        #{shipDynamicInfo.COG},
        #{shipDynamicInfo.ROT},
        #{shipDynamicInfo.SHIPTYPE},
        #{shipDynamicInfo.INPORTID},
        #{shipDynamicInfo.INPORTSTATE},
        #{shipDynamicInfo.ReportTime},
        sysdate
        );
    </insert>

    <insert id="insertHistoryTrack">
        insert into SHIP.SHIP_HISTORYTRACKPOINT_TMP(STATICSHIPID, BDID, MMSI, LON, LAT, SPEED, COG, REPORTTIME, LOADTIME)
        VALUES(
        #{shipDynamicInfo.StaticShipId},
        #{shipDynamicInfo.BDID},
        #{shipDynamicInfo.MMSI},
        #{shipDynamicInfo.LON},
        #{shipDynamicInfo.LAT},
        #{shipDynamicInfo.SPEED},
        #{shipDynamicInfo.COG},
        #{shipDynamicInfo.ReportTime},
        sysdate
        );
    </insert>

    <insert id="InsertOneAlarmInfo">
        insert into SHIP.ALARMRECORDINFO(SHIPNAME, STATICSHIPID, PEOPLEID, BDID, PORTID, LON, LAT, TYPE, STATE, LOADTIME, CONTENT, UPDATETIME, model)
        VALUES(
            #{shipDynamicInfo.SHIPNAME},
            #{shipDynamicInfo.staticShipId},
            0,
            #{shipDynamicInfo.BDID},
            0,
            #{shipDynamicInfo.LON},
            #{shipDynamicInfo.LAT},
            #{type},
            0,
            #{shipDynamicInfo.LoadTime},
            #{msg},
            #{shipDynamicInfo.LoadTime},
            2
        );
    </insert>

    <insert id="insertHistoryTrack_HQ">
        insert into SHIP.SHIP_HISTORYTRACKPOINT_HQ(STATICSHIPID, BDID, MMSI, LON, LAT, SPEED, COG, REPORTTIME, LOADTIME)
        VALUES(
                  #{shipDynamicInfo.StaticShipId},
                  #{shipDynamicInfo.BDID},
                  #{shipDynamicInfo.MMSI},
                  #{shipDynamicInfo.LON},
                  #{shipDynamicInfo.LAT},
                  #{shipDynamicInfo.SPEED},
                  #{shipDynamicInfo.COG},
                  #{shipDynamicInfo.ReportTime},
                  sysdate
              );
    </insert>

    <select id="selectStaticIdByBdId" resultType="java.lang.Integer">
        SELECT ID FROM SHIP.SHIP_STATICINFO WHERE BDID like concat('%',#{BDID},'%') limit 1;
    </select>

    <resultMap id="termBDid" type="com.bdstar.data.model.Bdid">
        <result column="TERMNO" jdbcType="VARCHAR" property="bdid" />
    </resultMap>

    <select id="selectTermBdIdByShipId" resultType="com.bdstar.data.model.Bdid" resultMap="termBDid">
        SELECT TERMNO FROM FISHER_AND_TERM WHERE TERMTYPE = 1 and SHIPID = #{shipId}
    </select>
    <select id="selectStaticInfoByMMSI" resultType="java.lang.Integer">
        SELECT COUNT (id) from SHIP.SHIP_STATICINFO WHERE MMSI LIKE concat('%',#{MMSI},'%');
    </select>
    <select id="selectStaticIdByMMSI" resultType="java.lang.Integer">
        SELECT ID FROM SHIP.SHIP_STATICINFO WHERE MMSI like concat('%',#{MMSI},'%') limit 1;
    </select>
    <select id="getAISShipNameByMMSI" resultType="java.lang.String">
        SELECT VEHICLENO FROM SHIP.AIS_RPTNAME WHERE TERMNO like concat('%',#{mmsi},'%') limit 1;
    </select>
    <select id="getShipNameByBDId" resultType="java.lang.String">
        SELECT shipName FROM SHIP.SHIP_STATICINFO WHERE BDID like concat('%',#{bdId},'%') limit 1;
    </select>
    <select id="getBdMsg" resultType="com.bdstar.data.model.BdMsg">
        SELECT ID, BDID, MSG FROM SHIP.BDSENDRECORD WHERE BSEND = 0;
    </select>

    <select id="getFisherByShipId" resultType="com.bdstar.data.model.ShipStaticInfo">
        SELECT * FROM FISHER WHERE shipId = #{shipId}
    </select>

    <select id="selectMmsiByShipId" resultType="java.lang.String">
        SELECT TERMNO FROM FISHER_AND_TERM WHERE TERMTYPE in (2,18) and SHIPID = #{shipId}
    </select>
    <select id="selectStaticIdByShipId" resultType="java.lang.Integer">
        SELECT ID FROM SHIP.SHIP_STATICINFO WHERE shipid = #{shipId} limit 1;
    </select>
    <select id="selectStaticInfoByShipId" resultType="java.lang.Integer">
        SELECT count(ID) FROM SHIP.SHIP_STATICINFO WHERE shipid = #{shipId};
    </select>
    <select id="getTyphoonAlarm" resultType="java.lang.Integer">
        SELECT COUNT(ID) FROM SHIP.ALARMRECORDINFO where loadtime >= #{time} and StaticShipId = #{shipDynamicInfo.StaticShipId} and type = 10 ;
    </select>

    <update id="updateDynamicShipInfo_byBDId">
        UPDATE SHIP.SHIP_DYNAMICTION SET LON = #{shipDynamicInfo.LON},
                                         LAT = #{shipDynamicInfo.LAT},
                                         SPEED = #{shipDynamicInfo.SPEED},
                                         COG = #{shipDynamicInfo.COG},
                                         REPORTTIME = #{shipDynamicInfo.ReportTime},
                                         LOADTIME = sysdate,
                                         LASTPOSTERMNO = #{shipDynamicInfo.LastPosTermNo},
                                         LASTPOSBDID = #{shipDynamicInfo.LastPosTermNo},
                                         BDTIME = #{shipDynamicInfo.BdTime}
        WHERE BDID like concat('%',#{shipDynamicInfo.BDID},'%');
    </update>
    <update id="updateDynamicShipInfo_byMMSI">
        UPDATE SHIP.SHIP_DYNAMICTION SET LON = #{shipDynamicInfo.LON},
                                         LAT = #{shipDynamicInfo.LAT},
                                         SPEED = #{shipDynamicInfo.SPEED},
                                         COG = #{shipDynamicInfo.COG},
                                         REPORTTIME = #{shipDynamicInfo.ReportTime},
                                         LOADTIME = sysdate,
                                         LASTPOSTERMNO = #{shipDynamicInfo.LastPosTermNo},
                                         AISTIME = #{shipDynamicInfo.AisTime}
        WHERE MMSI like concat('%',#{shipDynamicInfo.MMSI},'%');
    </update>
    <update id="UpdateOneAlarmInfo">
        update SHIP.ALARMRECORDINFO set loadtime = sysdate where StaticShipId = #{shipDynamicInfo.StaticShipId} and loadtime >= #{time};
    </update>

    <!-- 批量操作SQL - HQ数据处理优化 -->

    <!-- 批量插入静态船舶信息 -->
    <insert id="batchInsertStaticShipInfo">
        insert into SHIP.SHIP_STATICINFO
        (SHIPID, BDID, MMSI, SHIPNAME, IMO, CALLSIGN, OWNER, LXDH, LXDZ, CTCL, DW,
        ZJGLQW, SSDW, SHIPTYPE, LENGTH, WIDTH, BOUTSIDE, NATION, LOADTIME, BFOCUS, BBLACKFUXIU, BWHITEFUXIU)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.SHIPID}, #{item.BDID}, #{item.MMSI}, #{item.SHIPNAME}, #{item.IMO}, #{item.CallSign},
            #{item.Owner}, #{item.LXDH}, #{item.LXDZ}, #{item.CTCL}, #{item.DW}, #{item.ZJGLQW},
            #{item.SSDW}, #{item.SHIPTYPE}, #{item.length}, #{item.width}, #{item.bOutside},
            #{item.nation}, sysdate, #{item.bFocus}, #{item.bBlackFuXiu}, #{item.bWhiteFuXiu})
        </foreach>
    </insert>

    <!-- 批量插入动态船舶信息 -->
    <insert id="batchInsertDynamicShipInfo">
        insert into SHIP.SHIP_DYNAMICTION(STATICSHIPID, BDID, MMSI, SHIPNAME, CALLSIGN, LON, LAT, SPEED, COG, ROT, SHIPTYPE, INPORTID, INPORTSTATE, REPORTTIME, LOADTIME)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.StaticShipId}, #{item.BDID}, #{item.MMSI}, #{item.SHIPNAME}, #{item.callSign},
            #{item.LON}, #{item.LAT}, #{item.SPEED}, #{item.COG}, #{item.ROT}, #{item.SHIPTYPE},
            #{item.INPORTID}, #{item.INPORTSTATE}, #{item.ReportTime}, sysdate)
        </foreach>
    </insert>

    <!-- 批量更新动态船舶信息 - 按BDID -->
    <update id="batchUpdateDynamicShipInfo_byBDId">
        <foreach collection="list" item="item" separator=";">
            UPDATE SHIP.SHIP_DYNAMICTION SET
                LON = #{item.LON},
                LAT = #{item.LAT},
                SPEED = #{item.SPEED},
                COG = #{item.COG},
                REPORTTIME = #{item.ReportTime},
                LOADTIME = sysdate,
                LASTPOSTERMNO = #{item.LastPosTermNo},
                LASTPOSBDID = #{item.LastPosTermNo},
                BDTIME = #{item.BdTime}
            WHERE BDID like concat('%',#{item.BDID},'%')
        </foreach>
    </update>

    <!-- 批量更新动态船舶信息 - 按MMSI -->
    <update id="batchUpdateDynamicShipInfo_byMMSI">
        <foreach collection="list" item="item" separator=";">
            UPDATE SHIP.SHIP_DYNAMICTION SET
                LON = #{item.LON},
                LAT = #{item.LAT},
                SPEED = #{item.SPEED},
                COG = #{item.COG},
                REPORTTIME = #{item.ReportTime},
                LOADTIME = sysdate,
                LASTPOSTERMNO = #{item.LastPosTermNo},
                AISTIME = #{item.AisTime}
            WHERE MMSI like concat('%',#{item.MMSI},'%')
        </foreach>
    </update>

    <!-- 批量插入历史轨迹 - HQ -->
    <insert id="batchInsertHistoryTrack_HQ">
        insert into SHIP.SHIP_HISTORYTRACKPOINT_HQ(STATICSHIPID, BDID, MMSI, LON, LAT, SPEED, COG, REPORTTIME, LOADTIME)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.StaticShipId}, #{item.BDID}, #{item.MMSI}, #{item.LON}, #{item.LAT},
            #{item.SPEED}, #{item.COG}, #{item.ReportTime}, sysdate)
        </foreach>
    </insert>

    <!-- 批量查询静态ID - 按BDID -->
    <select id="batchSelectStaticIdByBdId" resultType="java.lang.Integer">
        SELECT ID FROM SHIP.SHIP_STATICINFO WHERE
        <foreach collection="bdIdList" item="bdId" separator=" OR ">
            BDID like concat('%',#{bdId},'%')
        </foreach>
        ORDER BY ID
    </select>

    <!-- 批量查询静态ID - 按MMSI -->
    <select id="batchSelectStaticIdByMMSI" resultType="java.lang.Integer">
        SELECT ID FROM SHIP.SHIP_STATICINFO WHERE
        <foreach collection="mmsiList" item="mmsi" separator=" OR ">
            MMSI like concat('%',#{mmsi},'%')
        </foreach>
        ORDER BY ID
    </select>

</mapper>
